# PowerShell Script to Clean Up Extra Images in assets/img Folders
# This script removes unnecessary duplicate images from assets/img folders

Write-Host "Cleaning up extra images in assets/img folders..." -ForegroundColor Green

# Get all game directories
$gameFolders = Get-ChildItem -Directory | Where-Object { 
    $_.Name -notlike "*.ps1" -and 
    $_.Name -ne "*.html" -and
    $_.Name -ne "*.md" -and
    $_.Name -ne "Example Game" -and
    (Test-Path (Join-Path $_.FullName "assets\img"))
}

$totalCleaned = 0

foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    
    Write-Host "`n📁 Processing: $gameName" -ForegroundColor Yellow
    
    # Get all files in assets/img
    $allFiles = Get-ChildItem $assetsImgPath -File
    
    # Required files that should stay
    $requiredFiles = @("gameicon.png", "game currency.png", "background.jpg", "favicon.ico", "mgi.png")
    
    # Files to remove (duplicates or unnecessary)
    $filesToRemove = @()
    
    foreach ($file in $allFiles) {
        $fileName = $file.Name
        
        # Skip required files
        if ($requiredFiles -contains $fileName) {
            Write-Host "  ✅ Keeping: $fileName" -ForegroundColor Green
            continue
        }
        
        # Check if it's a duplicate or unnecessary file
        if ($fileName -like "*$gameName*" -or 
            $fileName -like "*images*" -or
            $fileName -like "*removebg*" -or
            $fileName -like "*.jpg" -and $fileName -ne "background.jpg" -or
            $fileName -like "*.png" -and $fileName -notin @("gameicon.png", "game currency.png", "mgi.png")) {
            
            $filesToRemove += $file
            Write-Host "  Will remove: $fileName" -ForegroundColor Red
        } else {
            Write-Host "  Unknown file: $fileName" -ForegroundColor Yellow
        }
    }
    
    # Remove unnecessary files
    foreach ($fileToRemove in $filesToRemove) {
        try {
            Remove-Item $fileToRemove.FullName -Force
            Write-Host "    Removed: $($fileToRemove.Name)" -ForegroundColor Gray
            $totalCleaned++
        } catch {
            Write-Host "    ❌ Error removing $($fileToRemove.Name): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # Show final status
    $finalFiles = Get-ChildItem $assetsImgPath -File
    Write-Host "  📊 Final file count: $($finalFiles.Count)" -ForegroundColor Cyan
    
    # Verify required files exist
    $hasGameIcon = Test-Path (Join-Path $assetsImgPath "gameicon.png")
    $hasCurrency = Test-Path (Join-Path $assetsImgPath "game currency.png")
    $hasBackground = Test-Path (Join-Path $assetsImgPath "background.jpg")
    
    if ($hasGameIcon -and $hasCurrency -and $hasBackground) {
        Write-Host "  🎉 Complete and clean!" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️  Missing required files" -ForegroundColor Yellow
        if (!$hasGameIcon) { Write-Host "    - gameicon.png" -ForegroundColor Red }
        if (!$hasCurrency) { Write-Host "    - game currency.png" -ForegroundColor Red }
        if (!$hasBackground) { Write-Host "    - background.jpg" -ForegroundColor Red }
    }
}

Write-Host "`n=== Cleanup Summary ===" -ForegroundColor Magenta
Write-Host "Games processed: $($gameFolders.Count)" -ForegroundColor Cyan
Write-Host "Files removed: $totalCleaned" -ForegroundColor Green

# Final verification
Write-Host "`nFinal verification..." -ForegroundColor Blue
$cleanGames = 0
$completeGames = 0

foreach ($folder in $gameFolders) {
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    $files = Get-ChildItem $assetsImgPath -File
    
    # Check if only required files exist
    $requiredFiles = @("gameicon.png", "game currency.png", "background.jpg", "favicon.ico", "mgi.png")
    $extraFiles = $files | Where-Object { $_.Name -notin $requiredFiles }
    
    if ($extraFiles.Count -eq 0) {
        $cleanGames++
    }
    
    # Check if complete
    $hasAll = (Test-Path (Join-Path $assetsImgPath "gameicon.png")) -and
              (Test-Path (Join-Path $assetsImgPath "game currency.png")) -and
              (Test-Path (Join-Path $assetsImgPath "background.jpg"))
    
    if ($hasAll) { $completeGames++ }
}

$cleanRate = [math]::Round(($cleanGames / $gameFolders.Count) * 100, 1)
$completeRate = [math]::Round(($completeGames / $gameFolders.Count) * 100, 1)

Write-Host "Games with clean assets/img: $cleanGames/$($gameFolders.Count) ($cleanRate%)" -ForegroundColor Green
Write-Host "Games with complete image sets: $completeGames/$($gameFolders.Count) ($completeRate%)" -ForegroundColor Green

if ($cleanRate -eq 100) {
    Write-Host "`n🎉 Perfect! All assets/img folders are clean!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  Some folders may need additional cleanup" -ForegroundColor Yellow
}

Write-Host "`nAssets/img cleanup completed!" -ForegroundColor Blue
