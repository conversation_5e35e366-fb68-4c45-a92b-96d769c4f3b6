# PowerShell Script to Test Flexible Image Naming
# This script creates test files with different extensions to verify the flexible organizer works

Write-Host "Testing flexible image naming..." -ForegroundColor Green

# Create a test game folder
$testGameName = "Test Game"
$testGamePath = $testGameName

if (!(Test-Path $testGamePath)) {
    New-Item -ItemType Directory -Path $testGamePath -Force | Out-Null
    Write-Host "Created test game folder: $testGameName" -ForegroundColor Cyan
}

# Create assets folder structure
$assetsPath = Join-Path $testGamePath "assets"
$imgPath = Join-Path $assetsPath "img"

if (!(Test-Path $assetsPath)) { New-Item -ItemType Directory -Path $assetsPath -Force | Out-Null }
if (!(Test-Path $imgPath)) { New-Item -ItemType Directory -Path $imgPath -Force | Out-Null }

Write-Host "`nCreating test files with different extensions..." -ForegroundColor Yellow

# Test files with different extensions and no extensions
$testFiles = @(
    @{ name = "gameicon.webp"; content = "# Test gameicon with .webp extension" }
    @{ name = "game currency.bmp"; content = "# Test currency with .bmp extension" }
    @{ name = "background"; content = "# Test background with no extension" }
    @{ name = "gameicon"; content = "# Test gameicon with no extension" }
    @{ name = "game currency"; content = "# Test currency with no extension" }
    @{ name = "background.gif"; content = "# Test background with .gif extension" }
)

foreach ($testFile in $testFiles) {
    $filePath = Join-Path $testGamePath $testFile.name
    $testFile.content | Out-File -FilePath $filePath -Encoding UTF8
    Write-Host "  📄 Created: $($testFile.name)" -ForegroundColor Gray
}

Write-Host "`nTest files created. Now running flexible organizer on test game..." -ForegroundColor Blue

# Run the flexible organizer on just this test game
$testFolder = Get-Item $testGamePath
$assetsImgPath = Join-Path $testFolder.FullName "assets\img"

# Get all potential image files
$allFiles = Get-ChildItem $testFolder.FullName -File | Where-Object { 
    $_.Name -notlike "*.html" -and $_.Name -notlike "*.ps1"
}

Write-Host "`nFound files to process:" -ForegroundColor Cyan
foreach ($file in $allFiles) {
    Write-Host "  📷 $($file.Name)" -ForegroundColor Gray
}

$gameIconSet = $false
$currencySet = $false
$backgroundSet = $false

foreach ($file in $allFiles) {
    $fileName = $file.Name
    $fileBaseName = $file.BaseName
    $filePath = $file.FullName
    
    Write-Host "`nProcessing: $fileName" -ForegroundColor Yellow
    
    # Check exact name matches first (highest priority)
    if ($fileBaseName -eq "gameicon" -or $fileName -eq "gameicon") {
        if (!$gameIconSet) {
            $targetPath = Join-Path $assetsImgPath "gameicon.png"
            Copy-Item $filePath $targetPath -Force
            Write-Host "  ✅ Moved to gameicon.png (exact name match)" -ForegroundColor Green
            $gameIconSet = $true
        }
    }
    elseif ($fileBaseName -eq "game currency" -or $fileName -eq "game currency") {
        if (!$currencySet) {
            $targetPath = Join-Path $assetsImgPath "game currency.png"
            Copy-Item $filePath $targetPath -Force
            Write-Host "  ✅ Moved to game currency.png (exact name match)" -ForegroundColor Green
            $currencySet = $true
        }
    }
    elseif ($fileBaseName -eq "background" -or $fileName -eq "background") {
        if (!$backgroundSet) {
            $targetPath = Join-Path $assetsImgPath "background.jpg"
            Copy-Item $filePath $targetPath -Force
            Write-Host "  ✅ Moved to background.jpg (exact name match)" -ForegroundColor Green
            $backgroundSet = $true
        }
    }
    else {
        Write-Host "  ⚠️  No exact match found for: $fileName" -ForegroundColor Yellow
    }
}

Write-Host "`nTest Results:" -ForegroundColor Magenta
$hasGameIcon = Test-Path (Join-Path $assetsImgPath "gameicon.png")
$hasCurrency = Test-Path (Join-Path $assetsImgPath "game currency.png")
$hasBackground = Test-Path (Join-Path $assetsImgPath "background.jpg")

if ($hasGameIcon) { Write-Host "  ✅ gameicon.png created" -ForegroundColor Green } else { Write-Host "  ❌ gameicon.png missing" -ForegroundColor Red }
if ($hasCurrency) { Write-Host "  ✅ game currency.png created" -ForegroundColor Green } else { Write-Host "  ❌ game currency.png missing" -ForegroundColor Red }
if ($hasBackground) { Write-Host "  ✅ background.jpg created" -ForegroundColor Green } else { Write-Host "  ❌ background.jpg missing" -ForegroundColor Red }

if ($hasGameIcon -and $hasCurrency -and $hasBackground) {
    Write-Host "`n🎉 Test PASSED! Flexible naming works correctly!" -ForegroundColor Green
} else {
    Write-Host "`n❌ Test FAILED! Some files were not processed correctly." -ForegroundColor Red
}

Write-Host "`nCleaning up test files..." -ForegroundColor Blue
foreach ($file in $allFiles) {
    try {
        Remove-Item $file.FullName -Force
        Write-Host "  🗑️  Removed: $($file.Name)" -ForegroundColor Gray
    } catch {
        Write-Host "  ⚠️  Could not remove: $($file.Name)" -ForegroundColor Yellow
    }
}

Write-Host "`n📋 Flexible Naming Rules Confirmed:" -ForegroundColor Blue
Write-Host "  ✅ 'gameicon' (any extension) → gameicon.png" -ForegroundColor Green
Write-Host "  ✅ 'game currency' (any extension) → game currency.png" -ForegroundColor Green
Write-Host "  ✅ 'background' (any extension) → background.jpg" -ForegroundColor Green
Write-Host "  ✅ Files without extensions are supported!" -ForegroundColor Yellow

Write-Host "`n🎯 Usage Examples:" -ForegroundColor Cyan
Write-Host "  gameicon.jpg → gameicon.png" -ForegroundColor White
Write-Host "  gameicon.webp → gameicon.png" -ForegroundColor White
Write-Host "  gameicon → gameicon.png" -ForegroundColor White
Write-Host "  game currency.bmp → game currency.png" -ForegroundColor White
Write-Host "  game currency → game currency.png" -ForegroundColor White
Write-Host "  background.gif → background.jpg" -ForegroundColor White
Write-Host "  background → background.jpg" -ForegroundColor White
