﻿$(document).ready(function() {
	
	function resizedR(parentsize, childsize){
		childsize.style.width = parentsize.offsetWidth;
		childsize.style.height = parentsize.offsetHeight;
		childsize.style.borderRight = parentsize.offsetWidth + 'px solid rgba(0, 0, 0, 0.35)';
		childsize.style.borderTop = parentsize.offsetHeight + 'px solid transparent';
	}
	function resizedL(parentsize, childsize){
		childsize.style.width = parentsize.offsetWidth;
		childsize.style.height = parentsize.offsetHeight;
		childsize.style.borderRight = parentsize.offsetWidth + 'px solid transparent';
		childsize.style.borderTop = parentsize.offsetHeight + 'px solid rgba(0, 0, 0, 0.35)';
	}
	window.onload = function(){
		var content = document.querySelector('.prlw');
		var triangleR = document.querySelector('.prlw-r');
		var triangleL = document.querySelector('.prlw-l');
		resizedR(content,triangleR);
		resizedL(content,triangleL);
		window.onresize = function(e){
			resizedR(content,triangleR);
			resizedL(content,triangleL);
		}
	}
	
	// شاشة التحميل - تعمل بدون PHP
	$(window).load(function(){
		$('.prlw-r').animate({
			bottom: "-=10%",
			right: "-=10%"
		}, 700);
		$('.prlw-l').animate({
			top: "-=10%",
			left: "-=10%"
		}, 700);
		setTimeout(function(){
			$('.prlc-lb div').animate({
				width: "100%"
			}, 2000);
		}, 400);
		setTimeout(function(){
			$('.prlw-r').animate({
				bottom: "-=100%",
				right: "-=100%"
			}, 700);
			$('.prlw-l').animate({
				top: "-=100%",
				left: "-=100%"
			}, 700);
			$('.prlc').animate({
				opacity: "0"
			}, 300);			
		}, 2000);
		setTimeout(function(){
			$('.prlp').animate({
				top: "-=100%",
				opacity: 0
			}, 700, function() {
				$('.prlp').remove();
			});
			aO( '.aoi', 'animate__bounceIn' );
			$('.aoi').removeClass('aoinv');
		}, 2300);
	});	
	
	// تهيئة الأصوات
	var sounds = {
		buttonClick: new Audio('assets/sound/Button Click.mp3'),
		transition: new Audio('assets/sound/transition.mp3'),
		lastStep: new Audio('assets/sound/last step.mp3')
	};

	// ضبط مستوى الصوت
	Object.values(sounds).forEach(sound => {
		sound.volume = 0.5;
	});

	// دالة لتشغيل الصوت المحسنة
	function playSound(soundName) {
		try {
			// تشغيل الأصوات الموجودة
			if (sounds[soundName]) {
				sounds[soundName].currentTime = 0;
				sounds[soundName].play();
				return;
			}

			// أصوات إضافية لمرحلة مزامنة الجواهر
			switch(soundName) {
				case 'buttonClick':
				case 'cardAppear':
				case 'countStart':
					if (sounds['click']) {
						sounds['click'].currentTime = 0;
						sounds['click'].play();
					}
					break;
				case 'transition':
				case 'cardMove':
					if (sounds['whoosh']) {
						sounds['whoosh'].currentTime = 0;
						sounds['whoosh'].play();
					}
					break;
				case 'gemIncrement':
					// صوت خفيف لزيادة الجواهر
					if (sounds['coin']) {
						sounds['coin'].volume = 0.3;
						sounds['coin'].currentTime = 0;
						sounds['coin'].play();
					}
					break;
				case 'countComplete':
				case 'success':
					if (sounds['success']) {
						sounds['success'].currentTime = 0;
						sounds['success'].play();
					}
					break;
				case 'lastStep':
					if (sounds['complete']) {
						sounds['complete'].currentTime = 0;
						sounds['complete'].play();
					}
					break;
			}
		} catch (e) {
			console.log("Sound playback failed:", e);
		}
	}
	
	// تهيئة الجسيمات
	if($('#header-particles').length){
		// تحقق من وجود ملف التكوين
		if(typeof particlesJS !== 'undefined') {
			particlesJS('header-particles', {
				"particles": {
					"number": {
						"value": 80,
						"density": {
							"enable": true,
							"value_area": 800
						}
					},
					"color": {
						"value": "#ffffff"
					},
					"shape": {
						"type": "circle"
					},
					"opacity": {
						"value": 0.5,
						"random": false
					},
					"size": {
						"value": 3,
						"random": true
					},
					"line_linked": {
						"enable": true,
						"distance": 150,
						"color": "#ffffff",
						"opacity": 0.4,
						"width": 1
					},
					"move": {
						"enable": true,
						"speed": 6,
						"direction": "none",
						"random": false,
						"straight": false,
						"out_mode": "out",
						"bounce": false
					}
				},
				"interactivity": {
					"detect_on": "canvas",
					"events": {
						"onhover": {
							"enable": true,
							"mode": "repulse"
						},
						"onclick": {
							"enable": true,
							"mode": "push"
						},
						"resize": true
					}
				},
				"retina_detect": true
			});
		}
	}
	
	var jsp, jsp_i, peu;	
	jsp = "";	
	
	function fixplatformBox($platform_parent_class) {
        resetplatformBoxes();
        if ($platform_parent_class.hasClass('p-s-i-1')) {
            jsp = 'Windows PC';
            jsp_i = '<i class="fab fa-windows"></i>';
        }
        if ($platform_parent_class.hasClass('p-s-i-2')) {
            jsp = 'Xbox';
			jsp_i = '<i class="fab fa-xbox"></i>';
        }
		if ($platform_parent_class.hasClass('p-s-i-3')) {
            jsp = 'Playstation';
			jsp_i = '<i class="fab fa-playstation"></i>';
        }
		if ($platform_parent_class.hasClass('p-s-i-4')) {
            jsp = 'Android';
			jsp_i = '<i class="fab fa-android"></i>';
        }
		if ($platform_parent_class.hasClass('p-s-i-5')) {
            jsp = 'iOS';
			jsp_i = '<i class="fab fa-apple"></i>';
        }
        $platform_parent_class.addClass('active');
    }	
    function resetplatformBoxes() {
        var $platform_list = $('.p-s-i-1, .p-s-i-2, .p-s-i-3, .p-s-i-4, .p-s-i-5');	
        if ($platform_list.hasClass('active')) {
            $platform_list.removeClass('active');
        }
    }
	$('.p-s-i').click(function() {		
		if (!$(this).hasClass("active")) {
			aO( this, 'animate__headShake' );
		}	
		fixplatformBox($(this));
    });
	
	$c_s_m = ".prcs-m";
	$(document).on("click","#p-b-a",function() {
		playSound('buttonClick'); // تشغيل صوت النقر

		if($('#u-i').val() == '') {
			aO( $('.s-e-w-u'), 'animate__flipInX' );
			$('.s-e-w-u').fadeIn(function() {
				setTimeout(function(){
					$('.s-e-w-u').addClass('animate__animated animate__flipOutX').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function(){
						$(this).removeClass('animate__animated animate__flipOutX');
						$(this).hide();
					});
				}, 1200);
			});
		}
		if(jsp == '') {
			aO( $('.s-e-w-p'), 'animate__flipInX' );
			$('.s-e-w-p').fadeIn(function() {
				setTimeout(function(){
					$('.s-e-w-p').addClass('animate__animated animate__flipOutX').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function(){
						$(this).removeClass('animate__animated animate__flipOutX');
						$(this).hide();
					});
				}, 1200);
			});
		}
		if(jsp != '' && $('#u-i').val() != '') {
			peu = $('#u-i').val();
			playSound('transition'); // تشغيل صوت الانتقال
			showSuccessMessage();
		}
	});
	
	$('#u-i').on('keypress', function (e) {
         if(e.which === 13){
            $(this).attr("disabled", "disabled");
			if($('#u-i').val() == '') {
				aO( $('.s-e-w-u'), 'animate__flipInX' );
				$('.s-e-w-u').fadeIn(function() {			
					setTimeout(function(){
						$('.s-e-w-u').addClass('animate__animated animate__flipOutX').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function(){
							$(this).removeClass('animate__animated animate__flipOutX');
							$(this).hide();
						});
					}, 1200);
				});
				$('#u-i').removeAttr("disabled");
			}
			if(jsp == '') {
				aO( $('.s-e-w-p'), 'animate__flipInX' );
				$('.s-e-w-p').fadeIn(function() {					
					setTimeout(function(){
						$('.s-e-w-p').addClass('animate__animated animate__flipOutX').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function(){
							$(this).removeClass('animate__animated animate__flipOutX');
							$(this).hide();
						});
					}, 1200);
				});
				$('#u-i').removeAttr("disabled");
			}
			if(jsp != '' && $('#u-i').val() != '') {
				peu = $('#u-i').val();
				showSuccessMessage();
			}
         }
   });
	
	// دالة لإظهار مرحلة البحث الأولى (من search1.html)
	function showSuccessMessage() {
		// استخدام نفس HTML الموجود في search1.html مع إضافة container
		var searchHTML = `
			<div class="c-w c-w-u aoi">
				<div class="s-o-w">
					<div class="s-o aoi animation-delay-100">
						<span>1</span>
					</div>
				</div>
				<div class="s-f-p-w">
					<div class="s-f-p-i">
						<div class="s-f-p-t-w">
							<div class="s-f-p-t-l animate__animated animate__bounceIn"><div class="animate__animated animate__pulse animate__infinite">Searching...</div></div>
							<div class="caSs">
								<div class="caSsi">
									<div class="animate__animated animate__bounceIn animation-delay-100">
										<div class="s-f-p-t-u-w animate__animated animate__pulse animate__infinite"><div id="s-f-p-t-u-v" class="s-f-p-t-u-v">${peu}</div></div>
									</div>
									<div class="animate__animated animate__bounceIn animation-delay-200">
										<div class="s-f-p-t-p-w animate__animated animate__pulse animate__infinite"><div id="s-f-p-t-p-v" class="s-f-p-t-p-v">${jsp_i}</div></div>
									</div>
								</div>
							</div>
						</div>
						<div class="s-f-p-a-w animate__animated animate__bounceIn animation-delay-400">
							<img src="assets/img/mgi.png" class="mgi img-fluid ssa" style="width: 125px; height: 125px;">
						</div>
					</div>
				</div>
			</div>
		`;

		// تحديث المحتوى لإظهار مرحلة البحث
		$( ".c-w" ).addClass('animate__animated animate__bounceOut');
		setTimeout(function(){
			$( '.m-s .container' ).html(searchHTML).hide().fadeIn();
			aO( '.c-w-u', 'animate__bounceIn' );

			// الانتقال لمرحلة اختيار الجواهر بعد 3 ثواني
			setTimeout(function() {
				showCoinsSelection();
			}, 3000);
		}, 600);
	}

	// دالة لإظهار مرحلة اختيار كمية الجواهر (المرحلة 2)
	function showCoinsSelection() {
		// استخدام نفس HTML الموجود في step2.html
		var CoinsSelectionHTML = `
			<div class="acpeou">
				<div class="c-w c-w-r">
					<div class="s-o-w">
						<div class="s-o">
							<span>2</span>
						</div>
					</div>
					<div class="c-i-t">
						<span class="c-i-t-v">Please select the amount of Coins.</span>
					</div>
					<div class="r-i-s-o-w">
						<div class="r-i-s-i-w">
							<div class="r-i-s-w">
								<div class="r-i-s-r-w animate__animated animate__fadeIn animation-delay-200">
									<div class="r-i-s-r-h-w"><span>Selected Amount</span></div>
									<img src="assets/img/game currency.png" class="img-fluid r-i-s-r-w-i">
									<div class="r-i-s-r-m-w">
										<div class="r-i-s-r-m-v">2500</div>
										<div class="r-i-s-r-m-l">Coins</div>
									</div>
								</div>
							</div>
						</div>
						<div class="r-s-i-w">
							<div class="r-s-i r-s-i-1 active animation-delay-200">
								<img src="assets/img/game currency.png" class="img-fluid r-s-i-i">
								<div class="r-s-i-v">2500</div>
								<div class="r-s-i-l">Coins</div>
							</div>
							<div class="r-s-i r-s-i-2 animation-delay-400">
								<img src="assets/img/game currency.png" class="img-fluid r-s-i-i">
								<div class="r-s-i-v">5000</div>
								<div class="r-s-i-l">Coins</div>
							</div>
							<div class="r-s-i r-s-i-3 animation-delay-600">
								<img src="assets/img/game currency.png" class="img-fluid r-s-i-i">
								<div class="r-s-i-v">7500</div>
								<div class="r-s-i-l">Coins</div>
							</div>
							<div class="r-s-i r-s-i-4 animation-delay-800">
								<img src="assets/img/game currency.png" class="img-fluid r-s-i-i">
								<div class="r-s-i-v">10000</div>
								<div class="r-s-i-l">Coins</div>
							</div>
						</div>
					</div>
					<div class="p-b-w animate__animated animate__bounceIn animation-delay-1000">
						<div id="p-b-r" class="p-b"><span>Proceed</span></div>
					</div>
				</div>
			</div>
		`;

		// تحديث المحتوى لإظهار مرحلة اختيار الجواهر
		$( ".s-f-p-w" ).addClass('animate__animated animate__bounceOut');
		setTimeout(function(){
			$( '.m-s .container' ).html(CoinsSelectionHTML).hide().fadeIn();
			aO( '.c-w-r', 'animate__bounceIn' );

			// تفعيل وظائف اختيار الجواهر
			initCoinsSelection();
		}, 600);
	}

	// دالة لتفعيل وظائف اختيار الجواهر
	function initCoinsSelection() {
		// متغيرات الجواهر
		var selectedCoins = "2500";

		// وظيفة اختيار كمية الجواهر
		function fixCoinsBox($Coins_parent_class) {
			resetCoinsBoxes();
			if ($Coins_parent_class.hasClass('r-s-i-1')) {
				selectedCoins = '2500';
			}
			if ($Coins_parent_class.hasClass('r-s-i-2')) {
				selectedCoins = '5000';
			}
			if ($Coins_parent_class.hasClass('r-s-i-3')) {
				selectedCoins = '7500';
			}
			if ($Coins_parent_class.hasClass('r-s-i-4')) {
				selectedCoins = '10000';
			}
			$Coins_parent_class.addClass('active');

			// تحديث العرض
			$('.r-i-s-r-m-v').text(selectedCoins);
		}

		function resetCoinsBoxes() {
			var $Coins_list = $('.r-s-i-1, .r-s-i-2, .r-s-i-3, .r-s-i-4');
			if ($Coins_list.hasClass('active')) {
				$Coins_list.removeClass('active');
			}
		}

		// معالج النقر على خيارات الجواهر
		$('.r-s-i').click(function() {
			playSound('buttonClick'); // تشغيل صوت النقر
			$('.r-s-i').removeClass('animation-delay-200 animation-delay-400 animation-delay-600 animation-delay-800');
			if (!$(this).hasClass("active")) {
				aO( this, 'animate__headShake' );
				aO( '.r-i-s-r-m-v', 'animate__flipInX' );
			}
			fixCoinsBox($(this));
		});

		// معالج زر المتابعة لإظهار مرحلة البحث الثانية
		$(document).on("click","#p-b-r",function() {
			playSound('buttonClick'); // تشغيل صوت النقر
			showSearch2Screen(selectedCoins);
		});
	}

	// دالة لإظهار مرحلة البحث الثانية (من search2.html) مع تأثيرات محسنة
	function showSearch2Screen(Coins) {
		playSound('transition'); // تشغيل صوت الانتقال

		// استخدام نفس HTML الموجود في search2.html مع تحسينات
		var search2HTML = `
			<div class="sync-stage-container" style="position: relative; height: 550px;">
				<div id="top-sync-card" class="c-w c-w-p" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 10;">
					<div class="s-o-w">
						<div class="s-o">
							<span>3</span>
						</div>
					</div>
					<div class="p-i-w">
						<div class="prcs-l"><span class="material-icons-two-tone fa-spin animate__animated animate__rotateIn animate__infinite">settings</span></div>
						<div id="sync-status-text" class="prcs-m animate__animated animate__pulse animate__infinite">Loading...</div>
						<div id="pBC" class="p-lb">
							<div class="progress-bar-animated" style="width: 0%;">0%&nbsp;</div>
						</div>
					</div>
				</div>
				<!-- مربع خفي لإنشاء مسافة بين البطاقتين -->
				<div id="spacer-box" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 300px; height: 50px; z-index: 6; opacity: 0; pointer-events: none;"></div>
				<div id="bottom-Coins-card" class="Coins-card-container" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); opacity: 0; z-index: 5;">
					<div class="Coins-card-inner">
						<div class="Coins-card-content">
							<div class="Coins-image-container">
								<img src="assets/img/game currency.png" class="Coins-currency-image">
							</div>
							<div class="Coins-counter-container">
								<div class="Coins-counter-value">0</div>
								<div class="Coins-counter-label">Coins</div>
								<div class="Coins-progress-container">
									<div id="cwPB" class="Coins-progress-bar-wrapper">
										<div class="Coins-progress-bar" style="width: 0%;">0%&nbsp;</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		`;

		// تحديث المحتوى لإظهار مرحلة البحث الثانية
		$( ".acpeou" ).addClass('animate__animated animate__bounceOut');
		setTimeout(function(){
			$( '.m-s .container' ).html(search2HTML).hide().fadeIn();

			// إزالة أي تأثيرات bounce افتراضية وضبط الموضع الأولي
			$('#top-sync-card').removeClass('animate__bounceIn').css({
				'top': '50%',
				'left': '50%',
				'transform': 'translate(-50%, -50%)',
				'opacity': '1'
			});

			// بدء عملية العد التدريجي للجواهر مع تغيير النصوص والحركات
			startAdvancedCoinsAnimation(Coins);

			// الانتقال للمرحلة الثالثة بعد 9 ثواني (3+3+3)
			setTimeout(function() {
				showProcessingScreen(Coins);
			}, 9000);
		}, 600);
	}

	// دالة متقدمة لمرحلة مزامنة الجواهر مع حركات مرحلية
	function startAdvancedCoinsAnimation(targetCoins) {
		const targetValue = parseInt(targetCoins);
		const halfValue = Math.floor(targetValue / 2);

		// نصوص متغيرة للمراحل المختلفة
		const statusTexts = [
			"Loading...",
			"Getting ready...",
			`Syncing ${targetCoins} Coins for ${peu}...`,
			"Processing Coins...",
			"Almost done...",
			"Finalizing sync..."
		];

		let currentTextIndex = 0;

		// بدء تحريك شريط التقدم العلوي تدريجياً
		startTopProgressBar();

		// تغيير النص كل ثانية
		const textChangeInterval = setInterval(function() {
			if (currentTextIndex < statusTexts.length - 1) {
				currentTextIndex++;
				$('#sync-status-text').text(statusTexts[currentTextIndex]);
			}
		}, 1000);

		// المرحلة 1: البطاقة العلوية في المنتصف لمدة 3 ثواني
		setTimeout(function() {
			// تشغيل صوت الانتقال
			playSound('transition');

			// بدء صعود البطاقة العلوية مع تحسين السلاسة
			$('#top-sync-card').css({
				'transition': 'all 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
				'top': '20%',
				'transform': 'translate(-50%, -50%) scale(1.02)'
			});

			// بدء ظهور ونزول البطاقة السفلية
			setTimeout(function() {
				// تشغيل صوت ظهور البطاقة
				playSound('cardAppear');

				$('#bottom-Coins-card').css({
					'opacity': '1',
					'transition': 'all 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
					'top': '75%',
					'transform': 'translate(-50%, -50%) scale(1)'
				});

				// المرحلة 2: بدء عد الجواهر من النصف إلى الكامل لمدة 3 ثواني
				setTimeout(function() {
					// تشغيل صوت بداية العد
					playSound('countStart');
					startCoinsCountingPhase(halfValue, targetValue);

					// المرحلة 3: توقف عد الجواهر واستمرار البطاقة العلوية لمدة 3 ثواني
					setTimeout(function() {
						// إيقاف عد الجواهر والحفاظ على حركة البطاقة العلوية
						clearInterval(textChangeInterval);
						$('#sync-status-text').text(`Synced ${targetValue} Coins successfully!`);

						// تشغيل صوت الإنجاز
						playSound('success');

						// تأثير نهائي
						$('.Coins-counter-value').addClass('gem-flash');
						setTimeout(function() {
							$('.Coins-counter-value').removeClass('gem-flash');
						}, 1000);
					}, 3000);
				}, 1200);
			}, 600);
		}, 3000);
	}

	// دالة لتحريك شريط التقدم العلوي
	function startTopProgressBar() {
		const duration = 9000; // 9 ثواني إجمالي
		const startTime = Date.now();

		function animateTopProgress() {
			const elapsed = Date.now() - startTime;
			const progress = Math.min(elapsed / duration, 1);
			const percentage = Math.floor(progress * 100);

			$('.progress-bar-animated').css({
				'width': percentage + '%',
				'transition': 'width 0.1s ease-out'
			}).text(percentage + '%');

			if (progress < 1) {
				requestAnimationFrame(animateTopProgress);
			} else {
				$('.progress-bar-animated').css('width', '100%').text('100%');
			}
		}

		requestAnimationFrame(animateTopProgress);
	}

	// دالة عد الجواهر من النصف إلى الكامل
	function startCoinsCountingPhase(startValue, targetValue) {
		const duration = 3000; // 3 ثواني
		const startTime = Date.now();
		let currentCount = startValue;
		let currentProgress = 50; // البدء من 50%

		// تحديث القيم الأولية
		$('.Coins-counter-value').text(currentCount);
		$('.Coins-progress-bar').css('width', '50%').text('50%');

		function animateCoins() {
			const elapsed = Date.now() - startTime;
			const progress = Math.min(elapsed / duration, 1);

			// منحنى تسارع سلس
			const easeInOutQuad = progress < 0.5
				? 2 * progress * progress
				: 1 - Math.pow(-2 * progress + 2, 2) / 2;

			// حساب القيم الحالية
			const newCount = Math.floor(startValue + (targetValue - startValue) * easeInOutQuad);
			const newProgress = Math.floor(50 + 50 * easeInOutQuad);

			// تحديث العدادات
			if (newCount !== currentCount) {
				currentCount = newCount;
				$('.Coins-counter-value').text(currentCount);

				// تأثير وميض كل 250 جوهرة مع صوت
				if (currentCount > startValue && (currentCount - startValue) % 250 === 0) {
					$('.Coins-counter-value').addClass('animate__animated animate__pulse');
					playSound('gemIncrement'); // صوت زيادة الجواهر
					setTimeout(function() {
						$('.Coins-counter-value').removeClass('animate__animated animate__pulse');
					}, 400);
				}
			}

			// تحديث شريط التقدم السفلي
			if (newProgress !== currentProgress) {
				currentProgress = newProgress;
				$('.Coins-progress-bar').css({
					'width': currentProgress + '%',
					'transition': 'width 0.15s ease-out'
				}).text(currentProgress + '%');
			}

			// الاستمرار أو الانتهاء
			if (progress < 1) {
				requestAnimationFrame(animateCoins);
			} else {
				// القيم النهائية
				$('.Coins-counter-value').text(targetValue);
				$('.Coins-progress-bar').css('width', '100%').text('100%');

				// صوت إنجاز العد
				playSound('countComplete');
			}
		}

		requestAnimationFrame(animateCoins);
	}

	// دالة لإظهار المرحلة الثالثة (من step3.html)
	function showProcessingScreen(Coins) {
		// استخدام نفس HTML الموجود في step3.html
		var step3HTML = `
			<div class="c-w c-w-l animate__animated animate__bounceIn">
				<div class="s-o-w">
					<div class="s-o">
						<span class="material-icons-two-tone fa-spin">rotate_right</span>
					</div>
				</div>
				<div class="c-i-t">
					<h4 class="c-w-l-t-v animation-delay-100">Last Step</h4>
					<div class="c-w-l-p-v animation-delay-200">Hello <span class="lsv2s">${peu}</span>! You are almost done with synchronization of <span class="lsv2s">${Coins}</span> <span class="lsv2s">Coins</span>! Please complete the last step by clicking the button below to finish with synchronization process.</div>
				</div>
				<div class="c-w-l-r-o-w animation-delay-300">
					<div class="c-w-p-g-i-i animate__animated animate__pulse animate__infinite">
						<div class="c-w-p-g-i-i-c-w c-w-p-g-i-i-c-w-s"><span class="material-icons-two-tone mitt-r">check_circle</span></div>
						<div class="c-w-p-g-i-i-t"><img src="assets/img/game currency.png" class="img-fluid game-currency-final" style="width: 52px; height: 52px; border-radius: 50%; box-shadow: 0 0 15px rgba(76, 175, 80, 0.8);"></div>
						<div class="c-w-p-g-i-i-b">
							<div id="c-w-p-g-i-i-b-v" class="c-w-p-g-i-i-b-v">${Coins}</div>
							<div class="c-w-p-g-i-i-b-l">Coins</div>
						</div>
						<div class="cwPBw cwPBws">
							<div id="cwPB" class="p-lb"><div style="width: 150px;">100%&nbsp;</div></div>
						</div>
					</div>
				</div>
				<div class="p-b-w p-b-l-s-w animation-delay-400">
					<a id="l-s-v-b" class="p-b" href="finalstep.html" target="_blank"><span>Verify Now</span></a>
				</div>
			</div>
		`;

		$( ".acpeou" ).addClass('animate__animated animate__bounceOut');
		setTimeout(function(){
			$( '.m-s .container' ).html(step3HTML).hide().fadeIn();
			aO( '.c-w-l', 'animate__bounceIn' );

			// معالج زر التحقق النهائي
			$(document).on("click","#l-s-v-b",function() {
				playSound('lastStep'); // تشغيل صوت المرحلة الأخيرة
				showFinalSuccess(Coins);
			});
		}, 600);
	}

	// إضافة CSS للشاشات الجديدة
	if (!$('#enhanced-styles').length) {
		$('head').append(`
			<style id="enhanced-styles">
				/* تحديد حجم الصورة في مراحل البحث */
				.mgi.img-fluid.ssa {
					width: 125px !important;
					height: 125px !important;
					max-width: 125px !important;
					max-height: 125px !important;
				}

				/* تأثيرات شريط التقدم المحسن */
				.progress-bar-animated, .Coins-progress-bar {
					background: linear-gradient(90deg, #4CAF50, #66BB6A, #81C784, #66BB6A, #4CAF50);
					background-size: 300% 100%;
					animation: progressShine 3s ease-in-out infinite;
					color: white;
					font-weight: bold;
					text-align: center;
					line-height: inherit;
					border-radius: 5px;
					box-shadow: 0 3px 15px rgba(76, 175, 80, 0.6);
					transition: all 0.1s ease-out;
					position: relative;
					overflow: hidden;
				}

				.progress-bar-animated::before, .Coins-progress-bar::before {
					content: '';
					position: absolute;
					top: 0;
					left: -100%;
					width: 100%;
					height: 100%;
					background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
					animation: progressGloss 2s ease-in-out infinite;
				}

				@keyframes progressShine {
					0% { background-position: 300% 0; }
					50% { background-position: 0% 0; }
					100% { background-position: -300% 0; }
				}

				@keyframes progressGloss {
					0% { left: -100%; }
					50% { left: 100%; }
					100% { left: 100%; }
				}

				/* تحسين شريط التقدم العام */
				.p-lb {
					background: rgba(0,0,0,0.1);
					border-radius: 5px;
					overflow: hidden;
					box-shadow: inset 0 2px 5px rgba(0,0,0,0.2);
				}

				/* تحسينات للبطاقات المتحركة */
				.sync-stage-container {
					position: relative;
					height: 550px;
					width: 100%;
					max-width: 720px;
					margin: 0 auto;
					overflow: hidden;
				}

				#top-sync-card {
					transition: all 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
					box-shadow: 0 0 0 7px rgba(0, 0, 0, 0.3);
					background: #fff;
					border-radius: 7px;
					padding: 70px 120px 50px 120px;
					width: auto;
					max-width: 600px;
					will-change: transform, top;
					backface-visibility: hidden;
				}

				/* البطاقة السفلية المحسنة */
				.Coins-card-container {
					transition: all 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
					width: 250px;
					height: 162.09px;
					will-change: transform, opacity, top;
					backface-visibility: hidden;
				}

				.Coins-card-inner {
					background: #fff;
					border-radius: 7px;
					padding: 15px;
					box-shadow: 0 0 0 7px rgba(0, 0, 0, 0.3);
					position: relative;
					width: 100%;
					height: 100%;
					box-sizing: border-box;
				}

				/* عناصر البطاقة السفلية */
				.Coins-card-content {
					text-align: center;
					position: relative;
					height: 100%;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
				}

				.Coins-image-container {
					margin-bottom: 8px;
				}

				.Coins-currency-image {
					width: 45px;
					height: 45px;
					border-radius: 50%;
				}

				.Coins-counter-container {
					flex-grow: 1;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					width: 100%;
				}

				.Coins-counter-value {
					font-size: 1.8em;
					font-weight: bold;
					color: #333;
					margin-bottom: 3px;
					line-height: 1;
				}

				.Coins-counter-label {
					font-size: 1em;
					color: #666;
					font-weight: 500;
					margin-bottom: 8px;
				}

				/* شريط التقدم المحسن للبطاقة السفلية */
				.Coins-progress-container {
					width: 100%;
					margin-top: 0;
				}

				.Coins-progress-bar-wrapper {
					background: rgba(0,0,0,0.1);
					border-radius: 4px;
					overflow: hidden;
					box-shadow: inset 0 2px 5px rgba(0,0,0,0.2);
					height: 18px;
					width: 100%;
				}

				.Coins-progress-bar {
					background: linear-gradient(90deg, #9C27B0, #BA68C8, #9C27B0);
					background-size: 200% 100%;
					animation: progressShine 2s linear infinite;
					color: white;
					font-weight: bold;
					text-align: center;
					line-height: 18px;
					border-radius: 4px;
					transition: width 0.15s ease-out;
					box-shadow: 0 2px 8px rgba(156, 39, 176, 0.4);
					font-size: 0.85em;
				}

				/* تأثير الظهور التدريجي للبطاقة السفلية */
				#bottom-Coins-card.emerging {
					animation: emergeFromCard 1s ease-out forwards;
				}

				@keyframes emergeFromCard {
					0% {
						opacity: 0;
						transform: translate(-50%, -50%) scale(0.8);
					}
					50% {
						opacity: 0.7;
						transform: translate(-50%, -40%) scale(0.9);
					}
					100% {
						opacity: 1;
						transform: translate(-50%, -35%) scale(1);
					}
				}

				/* تحسين الحاوية الرئيسية */
				.m-s .container {
					position: relative;
					height: auto;
					min-height: 500px;
				}

				/* تحسينات للشاشات الصغيرة */
				@media screen and (max-width: 768px) {
					.sync-stage-container {
						height: 500px;
						padding: 0 20px;
					}

					#top-sync-card {
						padding: 50px 60px 40px 60px;
						max-width: 90%;
					}

					.Coins-card-container {
						width: 220px;
						height: 142px;
					}

					.Coins-card-inner {
						padding: 12px;
					}

					.Coins-counter-value {
						font-size: 1.5em;
					}

					.Coins-counter-label {
						font-size: 0.9em;
						margin-bottom: 6px;
					}

					.Coins-currency-image {
						width: 38px;
						height: 38px;
					}

					.Coins-progress-bar-wrapper {
						height: 16px;
					}

					.Coins-progress-bar {
						line-height: 16px;
						font-size: 0.75em;
					}
				}

				@media screen and (max-width: 480px) {
					.sync-stage-container {
						height: 450px;
						padding: 0 10px;
					}

					#top-sync-card {
						padding: 30px 40px 30px 40px;
					}

					.Coins-card-container {
						width: 200px;
						height: 130px;
					}

					.Coins-card-inner {
						padding: 10px;
					}

					.Coins-counter-value {
						font-size: 1.3em;
					}

					.Coins-counter-label {
						font-size: 0.8em;
						margin-bottom: 5px;
					}

					.Coins-currency-image {
						width: 32px;
						height: 32px;
					}

					.Coins-progress-bar-wrapper {
						height: 14px;
					}

					.Coins-progress-bar {
						line-height: 14px;
						font-size: 0.7em;
					}
				}

				/* تأثير وميض للجواهر */
				.gem-flash {
					animation: gemFlash 0.5s ease-in-out 2;
				}

				@keyframes gemFlash {
					0%, 100% {
						color: inherit;
						text-shadow: none;
					}
					50% {
						color: #FFD700;
						text-shadow: 0 0 10px #FFD700, 0 0 20px #FFD700;
					}
				}

				/* تحسين صورة العملة في المرحلة الأخيرة */
				.game-currency-final {
					animation: currencyGlow 2s ease-in-out infinite alternate;
				}

				@keyframes currencyGlow {
					0% {
						box-shadow: 0 0 15px rgba(76, 175, 80, 0.8);
					}
					100% {
						box-shadow: 0 0 25px rgba(76, 175, 80, 1), 0 0 35px rgba(76, 175, 80, 0.6);
					}
				}

				/* تأثيرات إضافية للمعالجة */
				.c-w-p-g-i-i-t-i {
					filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.8));
					transition: filter 0.3s ease;
				}

				.c-w-p-g-i-i-b-v {
					text-shadow: 0 0 15px rgba(255, 255, 255, 1);
					font-weight: bold;
					transition: all 0.3s ease;
				}

				/* تأثير النبض للأرقام */
				#sync-Coins-count, #c-w-p-g-i-i-b-v {
					transition: all 0.2s ease;
				}

				/* تأثيرات الوميض */
				@keyframes gemFlash {
					0%, 100% {
						color: #FFD700;
						text-shadow: 0 0 15px rgba(255, 215, 0, 1);
					}
					50% {
						color: #FFF;
						text-shadow: 0 0 25px rgba(255, 255, 255, 1);
						transform: scale(1.1);
					}
				}

				.gem-flash {
					animation: gemFlash 0.5s ease-in-out;
				}

				/* الشاشة النهائية */
				.c-w-final {
					background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
					border-radius: 15px;
					padding: 40px;
					color: white;
					text-align: center;
					max-width: 500px;
					margin: 0 auto;
				}

				.success-icon-container {
					margin: 20px 0;
				}

				.success-coin-icon {
					width: 120px;
					height: 120px;
					filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
					margin: 0 auto;
					display: block;
				}

				.final-content h2 {
					font-size: 2.5em;
					margin-bottom: 20px;
					color: #fff;
					font-weight: bold;
					text-shadow: 0 2px 4px rgba(0,0,0,0.3);
				}

				.final-content p {
					font-size: 1.2em;
					margin-bottom: 20px;
					text-shadow: 0 1px 2px rgba(0,0,0,0.2);
				}

				.final-details {
					background: rgba(255,255,255,0.1);
					padding: 20px;
					border-radius: 10px;
					margin: 20px 0;
					backdrop-filter: blur(10px);
					border: 1px solid rgba(255,255,255,0.2);
				}

				.final-details p {
					margin: 8px 0;
					font-size: 1.1em;
				}

				#restart-btn {
					margin-top: 20px;
					background: #fff;
					border: none;
					color: #4CAF50;
					padding: 15px 30px;
					border-radius: 25px;
					cursor: pointer;
					font-size: 16px;
					font-weight: bold;
					transition: all 0.3s ease;
					box-shadow: 0 4px 15px rgba(0,0,0,0.2);
				}

				#restart-btn:hover {
					background: #f0f0f0;
					transform: translateY(-2px);
					box-shadow: 0 6px 20px rgba(0,0,0,0.3);
				}

				/* تأثيرات إضافية للرسوم المتحركة */
				@keyframes coinGlow {
					0%, 100% { filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8)); }
					50% { filter: drop-shadow(0 0 30px rgba(255, 215, 0, 1)); }
				}

				.success-coin-icon {
					animation: coinGlow 2s ease-in-out infinite;
				}

				/* تصميم متجاوب */
				@media (max-width: 768px) {
					.c-w-final {
						padding: 20px;
					}

					.final-content h2 {
						font-size: 2em;
					}

					.success-coin-icon {
						width: 80px;
						height: 80px;
					}

					.mgi.img-fluid.ssa {
						width: 100px !important;
						height: 100px !important;
						max-width: 100px !important;
						max-height: 100px !important;
					}
				}
			</style>
		`);
	}

	// تم نقل معالج الأحداث إلى داخل الدوال المناسبة

	// دالة لإظهار النجاح النهائي مع صورة عملة اللعبة
	function showFinalSuccess(Coins) {
		var finalHTML = `
			<div class="c-w c-w-final aoi aoinv">
				<div class="s-o-w">
					<div class="s-o aoi aoinv animation-delay-100">
						<span>✓</span>
					</div>
				</div>
				<div class="final-content">
					<div class="success-icon-container animate__animated animate__bounceIn animation-delay-200">
						<img src="assets/img/game currency.png" class="success-coin-icon animate__animated animate__pulse animate__infinite" alt="Coins">
					</div>
					<h2 class="animate__animated animate__bounceIn animation-delay-400">🎉 Success!</h2>
					<p class="animate__animated animate__fadeIn animation-delay-600">Your ${Coins} Coins have been generated successfully!</p>
					<div class="final-details animate__animated animate__fadeInUp animation-delay-800">
						<p><strong>Username:</strong> ${peu}</p>
						<p><strong>Platform:</strong> ${jsp}</p>
						<p><strong>Generated:</strong> ${Coins} Coins</p>
						<p><strong>Status:</strong> Ready to use</p>
					</div>
					<button id="restart-btn" class="p-b animate__animated animate__bounceIn animation-delay-1000">Start Over</button>
				</div>
			</div>
		`;

		$( ".c-w-l" ).addClass('animate__animated animate__bounceOut');
		setTimeout(function(){
			$( '.m-s .container' ).html(finalHTML).hide().fadeIn();
			aO( '.c-w-final', 'animate__bounceIn' );
		}, 600);
	}

	// إعادة تشغيل التطبيق
	$(document).on("click","#restart-btn",function() {
		playSound('buttonClick'); // تشغيل صوت النقر
		setTimeout(function() {
			location.reload();
		}, 200);
	});
	
	function aO(el, anim, onDone) {
		var $el = $(el);
		// تحسين سلاسة الرسوم المتحركة
		$el.css({
			'animation-duration': '0.8s',
			'animation-timing-function': 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
			'animation-fill-mode': 'both'
		});
		$el.addClass( 'animate__animated ' + anim );
		$el.one( 'animationend', function() {
			$(this).removeClass( 'animate__animated ' + anim );
			onDone && onDone();
		});
	}
});
