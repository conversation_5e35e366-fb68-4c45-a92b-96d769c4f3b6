# PowerShell Script to Move Remaining PNG Files
# This script moves the remaining PNG files to their appropriate folders

Write-Host "Moving remaining PNG files..." -ForegroundColor Green

# Define the remaining files and their destinations
$fileMappings = @{
    "f.png" = @{ folder = "Fortnite"; newName = "Fortnite.png" }
    "p.png" = @{ folder = "PUBG Mobile"; newName = "PUBG Mobile.png" }
    "d.png" = @{ folder = "DLS 25"; newName = "DLS 25.png" }
    "e.png" = @{ folder = "eFootball"; newName = "eFootball.png" }
    "g.png" = @{ folder = "Golf Battle"; newName = "Golf Battle.png" }
    "FC MOBILE.jpg" = @{ folder = "FC MOBILE"; newName = "FC MOBILE.png" }
}

foreach ($file in $fileMappings.Keys) {
    if (Test-Path $file) {
        $mapping = $fileMappings[$file]
        $destinationFolder = $mapping.folder
        $newFileName = $mapping.newName
        $destinationPath = Join-Path $destinationFolder $newFileName
        
        if (Test-Path $destinationFolder) {
            try {
                Move-Item -Path $file -Destination $destinationPath -Force
                Write-Host "✅ Moved $file to $destinationFolder\$newFileName" -ForegroundColor Green
                
                # Remove old placeholder if it exists
                $oldPlaceholder = Join-Path $destinationFolder "$($mapping.folder).jpg"
                if (Test-Path $oldPlaceholder) {
                    try {
                        Remove-Item $oldPlaceholder -Force
                        Write-Host "   🗑️  Removed old placeholder" -ForegroundColor Yellow
                    } catch {
                        Write-Host "   ⚠️  Could not remove old placeholder: $($_.Exception.Message)" -ForegroundColor Yellow
                    }
                }
            } catch {
                Write-Host "❌ Failed to move $file : $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ Destination folder not found: $destinationFolder" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  File not found: $file" -ForegroundColor Yellow
    }
}

# Handle unknown files
$unknownFiles = @(
    "IconImage_20250509052312608_NEW_WAP_ICON_512_512.png",
    "IconImage_20250623084816771_NEW_WAP_ICON_512_512.png"
)

Write-Host "`nUnknown files that need manual identification:" -ForegroundColor Cyan
foreach ($file in $unknownFiles) {
    if (Test-Path $file) {
        Write-Host "❓ $file" -ForegroundColor Magenta
        Write-Host "   Please identify which game this belongs to and move it manually." -ForegroundColor Gray
    }
}

Write-Host "`nFile organization completed!" -ForegroundColor Green

# Show summary
Write-Host "`nSummary of games with PNG icons:" -ForegroundColor Blue
$gameFolders = Get-ChildItem -Directory | Where-Object { $_.Name -notlike "*.ps1" }
$gamesWithPNG = @()

foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    $pngFile = Join-Path $folder.FullName "$gameName.png"
    
    if (Test-Path $pngFile) {
        $gamesWithPNG += $gameName
    }
}

Write-Host "Total games with PNG icons: $($gamesWithPNG.Count)" -ForegroundColor Green
foreach ($game in $gamesWithPNG | Sort-Object) {
    Write-Host "  ✅ $game" -ForegroundColor Green
}
