# PowerShell Script to Organize Images with Flexible Extensions
# This script accepts images with any extension as long as the name matches

Write-Host "Organizing images with flexible extensions..." -ForegroundColor Green

# Get all game directories
$gameFolders = Get-ChildItem -Directory | Where-Object { 
    $_.Name -notlike "*.ps1" -and 
    $_.Name -ne "*.html" -and
    $_.Name -ne "*.md" -and
    (Test-Path (Join-Path $_.FullName "assets"))
}

$totalProcessed = 0
$totalMoved = 0

foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    Write-Host "`nProcessing: $gameName" -ForegroundColor Yellow
    
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    
    # Ensure assets/img folder exists
    if (!(Test-Path $assetsImgPath)) {
        New-Item -ItemType Directory -Path $assetsImgPath -Force | Out-Null
        Write-Host "  📁 Created assets/img folder" -ForegroundColor Cyan
    }
    
    # Get all image files in the game folder (excluding assets subfolder)
    $imageFiles = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg" -or $_.Extension -eq ".webp" -or $_.Extension -eq ".bmp" -or $_.Extension -eq ".gif") -and
        $_.Name -notlike "*.html"
    }
    
    # Also check for files without extensions that might be images
    $filesWithoutExt = Get-ChildItem $folder.FullName -File | Where-Object { 
        $_.Extension -eq "" -and
        ($_.Name -like "*gameicon*" -or $_.Name -like "*background*" -or $_.Name -like "*currency*")
    }
    
    # Combine both arrays
    $allImageFiles = $imageFiles + $filesWithoutExt
    
    $gameIconSet = $false
    $currencySet = $false
    $backgroundSet = $false
    
    foreach ($imageFile in $allImageFiles) {
        $fileName = $imageFile.Name
        $fileBaseName = $imageFile.BaseName  # Name without extension
        $filePath = $imageFile.FullName
        
        Write-Host "  📷 Found: $fileName" -ForegroundColor Gray
        
        # Determine the type of image based on name (ignoring extension)
        $targetPath = ""
        $targetName = ""
        
        # Check if it's a game icon (by name)
        if ($fileBaseName -eq "gameicon" -or $fileName -eq "gameicon") {
            if (!$gameIconSet) {
                $targetPath = Join-Path $assetsImgPath "gameicon.png"
                $targetName = "gameicon.png"
                $gameIconSet = $true
                Write-Host "    ➡️  Moving to gameicon.png (exact name match)" -ForegroundColor Green
            }
        }
        # Check if it's a currency image (by name)
        elseif ($fileBaseName -eq "game currency" -or $fileName -eq "game currency") {
            if (!$currencySet) {
                $targetPath = Join-Path $assetsImgPath "game currency.png"
                $targetName = "game currency.png"
                $currencySet = $true
                Write-Host "    ➡️  Moving to game currency.png (exact name match)" -ForegroundColor Magenta
            }
        }
        # Check if it's a background image (by name)
        elseif ($fileBaseName -eq "background" -or $fileName -eq "background") {
            if (!$backgroundSet) {
                $targetPath = Join-Path $assetsImgPath "background.jpg"
                $targetName = "background.jpg"
                $backgroundSet = $true
                Write-Host "    ➡️  Moving to background.jpg (exact name match)" -ForegroundColor Blue
            }
        }
        # Fallback: Check for partial matches or game-specific patterns
        elseif ($fileName -like "*$gameName*" -or 
                $fileName -like "*gameicon*" -or 
                $fileName -like "*icon*" -or
                $fileName -like "*logo*") {
            
            if (!$gameIconSet) {
                $targetPath = Join-Path $assetsImgPath "gameicon.png"
                $targetName = "gameicon.png"
                $gameIconSet = $true
                Write-Host "    ➡️  Moving to gameicon.png (pattern match)" -ForegroundColor Yellow
            }
        }
        # Check for currency patterns
        elseif ($fileName -like "*currency*" -or 
                $fileName -like "*coin*" -or 
                $fileName -like "*gem*" -or 
                $fileName -like "*diamond*" -or
                $fileName -like "*money*" -or
                $fileName -like "*token*" -or
                $fileName -like "*credit*" -or
                $fileName -like "*cash*" -or
                $fileName -like "*point*" -or
                $fileName -like "*buck*" -or
                $fileName -like "*robux*" -or
                $fileName -like "*uc*" -or
                $fileName -like "*cp*") {
            
            if (!$currencySet) {
                $targetPath = Join-Path $assetsImgPath "game currency.png"
                $targetName = "game currency.png"
                $currencySet = $true
                Write-Host "    ➡️  Moving to game currency.png (pattern match)" -ForegroundColor Yellow
            }
        }
        # Check for background patterns
        elseif ($fileName -like "*wallpaper*" -or 
                $fileName -like "*background*" -or 
                $fileName -like "*wall*" -or
                $fileName -like "*bg*") {
            
            if (!$backgroundSet) {
                $targetPath = Join-Path $assetsImgPath "background.jpg"
                $targetName = "background.jpg"
                $backgroundSet = $true
                Write-Host "    ➡️  Moving to background.jpg (pattern match)" -ForegroundColor Yellow
            }
        }
        
        # Move the file if target path is determined
        if ($targetPath -ne "" -and !(Test-Path $targetPath)) {
            try {
                Copy-Item $filePath $targetPath -Force
                $totalMoved++
                Write-Host "    ✅ Moved successfully to $targetName" -ForegroundColor Green
            } catch {
                Write-Host "    ❌ Error moving file: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
    
    # Clean up - remove original image files after copying
    foreach ($imageFile in $allImageFiles) {
        # Check if the image was successfully copied to assets/img
        $wasProcessed = $false
        
        if ((Test-Path (Join-Path $assetsImgPath "gameicon.png")) -or
            (Test-Path (Join-Path $assetsImgPath "game currency.png")) -or
            (Test-Path (Join-Path $assetsImgPath "background.jpg"))) {
            
            try {
                Remove-Item $imageFile.FullName -Force
                Write-Host "  🗑️  Removed original: $($imageFile.Name)" -ForegroundColor Gray
            } catch {
                Write-Host "  ⚠️  Could not remove: $($imageFile.Name)" -ForegroundColor Yellow
            }
        }
    }
    
    $totalProcessed++
    
    # Show status for this game
    $hasGameIcon = Test-Path (Join-Path $assetsImgPath "gameicon.png")
    $hasCurrency = Test-Path (Join-Path $assetsImgPath "game currency.png")
    $hasBackground = Test-Path (Join-Path $assetsImgPath "background.jpg")
    
    Write-Host "  Status:" -ForegroundColor Cyan
    if ($hasGameIcon) { Write-Host "    ✅ gameicon.png" -ForegroundColor Green } else { Write-Host "    ❌ gameicon.png" -ForegroundColor Red }
    if ($hasCurrency) { Write-Host "    ✅ game currency.png" -ForegroundColor Green } else { Write-Host "    ❌ game currency.png" -ForegroundColor Red }
    if ($hasBackground) { Write-Host "    ✅ background.jpg" -ForegroundColor Green } else { Write-Host "    ❌ background.jpg" -ForegroundColor Red }
}

Write-Host "`n=== Final Summary ===" -ForegroundColor Magenta
Write-Host "Games processed: $totalProcessed" -ForegroundColor Cyan
Write-Host "Images moved: $totalMoved" -ForegroundColor Green

# Count complete games
$completeGames = 0
foreach ($folder in $gameFolders) {
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    $hasAll = (Test-Path (Join-Path $assetsImgPath "gameicon.png")) -and
              (Test-Path (Join-Path $assetsImgPath "game currency.png")) -and
              (Test-Path (Join-Path $assetsImgPath "background.jpg"))
    if ($hasAll) { $completeGames++ }
}

Write-Host "Games with complete image sets: $completeGames" -ForegroundColor Green
Write-Host "Completion rate: $([math]::Round(($completeGames / $totalProcessed) * 100, 1))%" -ForegroundColor Magenta

Write-Host "`n📋 Supported file naming (any extension):" -ForegroundColor Blue
Write-Host "  🎮 gameicon (becomes gameicon.png)" -ForegroundColor Green
Write-Host "  💰 game currency (becomes game currency.png)" -ForegroundColor Green
Write-Host "  🖼️  background (becomes background.jpg)" -ForegroundColor Green
Write-Host "  📁 Files can have any extension or no extension at all!" -ForegroundColor Yellow

Write-Host "`n🎉 Flexible image organization completed!" -ForegroundColor Green
