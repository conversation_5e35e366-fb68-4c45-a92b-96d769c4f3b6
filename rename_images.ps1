# PowerShell Script to Rename Images According to New Convention
# This script renames existing images to the new naming convention

Write-Host "Renaming images to new convention..." -ForegroundColor Green

# Get all game directories
$gameFolders = Get-ChildItem -Directory | Where-Object { 
    $_.Name -notlike "*.ps1" -and 
    $_.Name -ne "*.html" -and 
    $_.Name -ne "Brawl Stars"  # Skip Brawl Stars as it's already correct
}

foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    Write-Host "Processing: $gameName" -ForegroundColor Yellow
    
    # Create assets/img folder if it doesn't exist
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    if (!(Test-Path $assetsImgPath)) {
        New-Item -ItemType Directory -Path $assetsImgPath -Force | Out-Null
        Write-Host "  📁 Created assets/img folder" -ForegroundColor Cyan
    }
    
    # Rename game icon
    $gameIconPng = Join-Path $folder.FullName "$gameName.png"
    $gameIconJpg = Join-Path $folder.FullName "$gameName.jpg"
    $newGameIcon = Join-Path $assetsImgPath "gameicon.png"
    
    if (Test-Path $gameIconPng) {
        Copy-Item $gameIconPng $newGameIcon -Force
        Write-Host "  ✅ Copied $gameName.png to gameicon.png" -ForegroundColor Green
    } elseif (Test-Path $gameIconJpg) {
        Copy-Item $gameIconJpg $newGameIcon -Force
        Write-Host "  ✅ Copied $gameName.jpg to gameicon.png" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️  No game icon found for $gameName" -ForegroundColor Yellow
    }
    
    # Find and rename currency image
    $currencyFiles = Get-ChildItem $folder.FullName -File | Where-Object { 
        $_.Name -notlike "wallpaper*" -and 
        $_.Name -ne "$gameName.jpg" -and 
        $_.Name -ne "$gameName.png" -and
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png")
    }
    
    if ($currencyFiles.Count -gt 0) {
        $currencyFile = $currencyFiles[0]  # Take the first currency file found
        $newCurrencyPath = Join-Path $assetsImgPath "game currency.png"
        Copy-Item $currencyFile.FullName $newCurrencyPath -Force
        Write-Host "  ✅ Copied $($currencyFile.Name) to game currency.png" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️  No currency image found for $gameName" -ForegroundColor Yellow
    }
    
    # Rename wallpaper to background
    $wallpaperFile = Join-Path $folder.FullName "wallpaper $gameName.jpg"
    $newBackgroundPath = Join-Path $assetsImgPath "background.jpg"
    
    if (Test-Path $wallpaperFile) {
        Copy-Item $wallpaperFile $newBackgroundPath -Force
        Write-Host "  ✅ Copied wallpaper to background.jpg" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️  No wallpaper found for $gameName" -ForegroundColor Yellow
    }
    
    Write-Host ""
}

Write-Host "Image renaming completed!" -ForegroundColor Green

# Show summary
Write-Host "`nSummary of renamed images:" -ForegroundColor Blue
$totalGames = 0
$gamesWithIcons = 0
$gamesWithCurrency = 0
$gamesWithBackground = 0

foreach ($folder in $gameFolders) {
    $totalGames++
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    
    if (Test-Path (Join-Path $assetsImgPath "gameicon.png")) { $gamesWithIcons++ }
    if (Test-Path (Join-Path $assetsImgPath "game currency.png")) { $gamesWithCurrency++ }
    if (Test-Path (Join-Path $assetsImgPath "background.jpg")) { $gamesWithBackground++ }
}

Write-Host "Total games processed: $totalGames" -ForegroundColor Cyan
Write-Host "Games with gameicon.png: $gamesWithIcons" -ForegroundColor Green
Write-Host "Games with game currency.png: $gamesWithCurrency" -ForegroundColor Green
Write-Host "Games with background.jpg: $gamesWithBackground" -ForegroundColor Green
