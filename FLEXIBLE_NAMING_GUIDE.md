# 🎯 دليل الأسماء المرنة للصور - Flexible Image Naming Guide

## ✅ النظام الجديد المحدث

تم تحديث نظام تنظيم الصور ليقبل **أي امتداد** أو **بدون امتداد** طالما أن الاسم صحيح!

## 📋 قواعد الأسماء المرنة

### 1. أيقونة اللعبة (Game Icon)
**الاسم المطلوب**: `gameicon`

**أمثلة مقبولة**:
- ✅ `gameicon.jpg` → `gameicon.png`
- ✅ `gameicon.png` → `gameicon.png`
- ✅ `gameicon.webp` → `gameicon.png`
- ✅ `gameicon.bmp` → `gameicon.png`
- ✅ `gameicon.gif` → `gameicon.png`
- ✅ `gameicon` (بدون امتداد) → `gameicon.png`

### 2. صورة العملة (Currency Image)
**الاسم المطلوب**: `game currency`

**أمثلة مقبولة**:
- ✅ `game currency.jpg` → `game currency.png`
- ✅ `game currency.png` → `game currency.png`
- ✅ `game currency.webp` → `game currency.png`
- ✅ `game currency.bmp` → `game currency.png`
- ✅ `game currency` (بدون امتداد) → `game currency.png`

### 3. خلفية اللعبة (Background)
**الاسم المطلوب**: `background`

**أمثلة مقبولة**:
- ✅ `background.jpg` → `background.jpg`
- ✅ `background.png` → `background.jpg`
- ✅ `background.webp` → `background.jpg`
- ✅ `background.gif` → `background.jpg`
- ✅ `background` (بدون امتداد) → `background.jpg`

## 🎯 كيف يعمل النظام

### الأولوية في التعرف على الصور:

1. **الأولوية الأولى**: التطابق التام للاسم
   - `gameicon` (أي امتداد أو بدون امتداد)
   - `game currency` (أي امتداد أو بدون امتداد)
   - `background` (أي امتداد أو بدون امتداد)

2. **الأولوية الثانية**: التطابق الجزئي
   - ملفات تحتوي على اسم اللعبة
   - ملفات تحتوي على كلمات مفتاحية (icon, currency, wallpaper, etc.)

## 🛠️ السكريبت المحدث

**السكريبت الجديد**: `flexible_image_organizer.ps1`

### المميزات الجديدة:
- ✅ يقبل أي امتداد (.jpg, .png, .webp, .bmp, .gif, .jpeg)
- ✅ يقبل ملفات بدون امتداد
- ✅ يعطي أولوية للأسماء الدقيقة
- ✅ ينظم الصور تلقائياً في `assets/img`
- ✅ يحول الصور للصيغة المطلوبة

## 📁 النتيجة النهائية

بغض النظر عن الامتداد الأصلي، ستحصل على:

```
اسم اللعبة/
└── assets/
    └── img/
        ├── gameicon.png       # من أي ملف اسمه gameicon
        ├── game currency.png  # من أي ملف اسمه game currency
        └── background.jpg     # من أي ملف اسمه background
```

## 🎮 أمثلة عملية

### مثال 1: Free Fire
**الملفات الأصلية**:
- `gameicon.webp`
- `game currency.bmp`
- `background`

**النتيجة**:
- `assets/img/gameicon.png`
- `assets/img/game currency.png`
- `assets/img/background.jpg`

### مثال 2: Fortnite
**الملفات الأصلية**:
- `gameicon`
- `game currency.gif`
- `background.png`

**النتيجة**:
- `assets/img/gameicon.png`
- `assets/img/game currency.png`
- `assets/img/background.jpg`

## 🚀 كيفية الاستخدام

1. **ضع الصور** في مجلد اللعبة بالأسماء الصحيحة
2. **شغل السكريبت**:
   ```powershell
   .\flexible_image_organizer.ps1
   ```
3. **تحقق من النتائج** في مجلد `assets/img`

## 📊 الفوائد

- **مرونة كاملة**: لا تحتاج للقلق بشأن الامتداد
- **سهولة الاستخدام**: فقط اسم الملف بشكل صحيح
- **توحيد تلقائي**: جميع الصور تصبح بنفس الصيغة
- **تنظيم مثالي**: كل شيء في مكانه الصحيح

## 🎉 الخلاصة

الآن يمكنك وضع أي صورة بأي امتداد (أو بدون امتداد) طالما أن الاسم صحيح:
- `gameicon` → أيقونة اللعبة
- `game currency` → صورة العملة
- `background` → خلفية اللعبة

**النظام سيتعرف عليها تلقائياً وينظمها في المكان الصحيح!** 🚀
