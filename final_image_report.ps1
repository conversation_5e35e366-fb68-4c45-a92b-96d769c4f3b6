# PowerShell Script to Generate Final Image Organization Report
# This script checks all games and provides a comprehensive report

Write-Host "=== Final Image Organization Report ===" -ForegroundColor Magenta
Write-Host ""

# Get all game directories
$gameFolders = Get-ChildItem -Directory | Where-Object { 
    $_.Name -notlike "*.ps1" -and 
    $_.Name -ne "*.html" -and
    $_.Name -ne "*.md" -and
    (Test-Path (Join-Path $_.FullName "assets"))
}

$totalGames = $gameFolders.Count
$completeGames = 0
$gamesWithGameIcon = 0
$gamesWithCurrency = 0
$gamesWithBackground = 0
$gamesWithExtraImages = 0

Write-Host "Checking $totalGames games..." -ForegroundColor Cyan
Write-Host ""

foreach ($folder in $gameFolders | Sort-Object Name) {
    $gameName = $folder.Name
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    
    # Check required images
    $gameIconPath = Join-Path $assetsImgPath "gameicon.png"
    $currencyPath = Join-Path $assetsImgPath "game currency.png"
    $backgroundPath = Join-Path $assetsImgPath "background.jpg"
    
    $hasGameIcon = Test-Path $gameIconPath
    $hasCurrency = Test-Path $currencyPath
    $hasBackground = Test-Path $backgroundPath
    
    # Count statistics
    if ($hasGameIcon) { $gamesWithGameIcon++ }
    if ($hasCurrency) { $gamesWithCurrency++ }
    if ($hasBackground) { $gamesWithBackground++ }
    if ($hasGameIcon -and $hasCurrency -and $hasBackground) { $completeGames++ }
    
    # Check for extra images in main folder
    $extraImages = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg") -and
        $_.Name -notlike "*.html"
    }
    
    if ($extraImages.Count -gt 0) { $gamesWithExtraImages++ }
    
    # Display status
    $status = ""
    if ($hasGameIcon -and $hasCurrency -and $hasBackground) {
        $status = "✅ COMPLETE"
        $color = "Green"
    } elseif ($hasGameIcon -or $hasCurrency -or $hasBackground) {
        $status = "⚠️  PARTIAL"
        $color = "Yellow"
    } else {
        $status = "❌ MISSING"
        $color = "Red"
    }
    
    Write-Host "$status - $gameName" -ForegroundColor $color
    
    # Show details for incomplete games
    if (!($hasGameIcon -and $hasCurrency -and $hasBackground)) {
        if (!$hasGameIcon) { Write-Host "    ❌ gameicon.png" -ForegroundColor Red }
        if (!$hasCurrency) { Write-Host "    ❌ game currency.png" -ForegroundColor Red }
        if (!$hasBackground) { Write-Host "    ❌ background.jpg" -ForegroundColor Red }
    }
    
    # Show extra images
    if ($extraImages.Count -gt 0) {
        Write-Host "    📷 Extra images in main folder: $($extraImages.Count)" -ForegroundColor Gray
        foreach ($extraImage in $extraImages) {
            Write-Host "      - $($extraImage.Name)" -ForegroundColor Gray
        }
    }
}

Write-Host ""
Write-Host "=== Summary Statistics ===" -ForegroundColor Blue
Write-Host "Total games: $totalGames" -ForegroundColor Cyan
Write-Host "Complete games (all 3 images): $completeGames" -ForegroundColor Green
Write-Host "Games with gameicon.png: $gamesWithGameIcon" -ForegroundColor Yellow
Write-Host "Games with game currency.png: $gamesWithCurrency" -ForegroundColor Yellow
Write-Host "Games with background.jpg: $gamesWithBackground" -ForegroundColor Yellow
Write-Host "Games with extra images: $gamesWithExtraImages" -ForegroundColor Red

$completionRate = [math]::Round(($completeGames / $totalGames) * 100, 1)
Write-Host ""
Write-Host "Overall completion rate: $completionRate%" -ForegroundColor Magenta

if ($completionRate -eq 100) {
    Write-Host "🎉 Perfect! All games have complete image sets!" -ForegroundColor Green
} elseif ($completionRate -ge 80) {
    Write-Host "👍 Great progress! Most games are complete." -ForegroundColor Yellow
} else {
    Write-Host "⚠️  More work needed to complete image organization." -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Image Organization Standards ===" -ForegroundColor Blue
Write-Host "Each game should have in assets/img/:" -ForegroundColor White
Write-Host "  🎮 gameicon.png - Game logo/icon" -ForegroundColor Green
Write-Host "  💰 game currency.png - Currency image" -ForegroundColor Green
Write-Host "  🖼️  background.jpg - Game wallpaper/background" -ForegroundColor Green
Write-Host "  🔧 favicon.ico, mgi.png - System files (auto-generated)" -ForegroundColor Gray

if ($gamesWithExtraImages -gt 0) {
    Write-Host ""
    Write-Host "⚠️  $gamesWithExtraImages games still have images outside assets/img folder" -ForegroundColor Yellow
    Write-Host "Run the organization script again to clean up remaining images." -ForegroundColor Yellow
}
