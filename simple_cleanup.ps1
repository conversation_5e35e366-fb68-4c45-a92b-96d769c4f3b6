# Simple PowerShell Script to Clean Up Images
# This script removes duplicate images and organizes folders

Write-Host "Starting simple cleanup..." -ForegroundColor Green

# Get all game directories
$gameFolders = Get-ChildItem -Directory | Where-Object { 
    $_.Name -notlike "*.ps1" -and 
    $_.Name -ne "*.html" -and
    $_.Name -ne "*.md" -and
    $_.Name -ne "Example Game"
}

$totalCleaned = 0

foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    Write-Host "Processing: $gameName" -ForegroundColor Yellow
    
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    
    # Ensure assets/img exists
    if (!(Test-Path $assetsImgPath)) {
        New-Item -ItemType Directory -Path $assetsImgPath -Force | Out-Null
    }
    
    # Move images from main folder to assets/img
    $mainImages = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg" -or $_.Extension -eq ".webp" -or $_.Extension -eq ".bmp" -or $_.Extension -eq ".gif") -and
        $_.Name -notlike "*.html"
    }
    
    foreach ($image in $mainImages) {
        $fileName = $image.Name
        $targetPath = Join-Path $assetsImgPath $fileName
        
        try {
            Move-Item $image.FullName $targetPath -Force
            Write-Host "  Moved: $fileName" -ForegroundColor Green
            $totalCleaned++
        } catch {
            Write-Host "  Error moving: $fileName" -ForegroundColor Red
        }
    }
    
    # Clean up assets/img folder
    if (Test-Path $assetsImgPath) {
        $allFiles = Get-ChildItem $assetsImgPath -File
        $requiredFiles = @("gameicon.png", "game currency.png", "background.jpg", "favicon.ico", "mgi.png")
        
        foreach ($file in $allFiles) {
            if ($file.Name -notin $requiredFiles) {
                # Check if it's a duplicate or can be renamed
                if ($file.Name -like "*$gameName*" -and !(Test-Path (Join-Path $assetsImgPath "gameicon.png"))) {
                    # Rename to gameicon.png
                    $newPath = Join-Path $assetsImgPath "gameicon.png"
                    try {
                        Rename-Item $file.FullName $newPath -Force
                        Write-Host "  Renamed to gameicon.png: $($file.Name)" -ForegroundColor Cyan
                    } catch {
                        Write-Host "  Error renaming: $($file.Name)" -ForegroundColor Red
                    }
                }
                elseif ($file.Name -like "*currency*" -and !(Test-Path (Join-Path $assetsImgPath "game currency.png"))) {
                    # Rename to game currency.png
                    $newPath = Join-Path $assetsImgPath "game currency.png"
                    try {
                        Rename-Item $file.FullName $newPath -Force
                        Write-Host "  Renamed to game currency.png: $($file.Name)" -ForegroundColor Cyan
                    } catch {
                        Write-Host "  Error renaming: $($file.Name)" -ForegroundColor Red
                    }
                }
                elseif ($file.Name -like "*background*" -and !(Test-Path (Join-Path $assetsImgPath "background.jpg"))) {
                    # Rename to background.jpg
                    $newPath = Join-Path $assetsImgPath "background.jpg"
                    try {
                        Rename-Item $file.FullName $newPath -Force
                        Write-Host "  Renamed to background.jpg: $($file.Name)" -ForegroundColor Cyan
                    } catch {
                        Write-Host "  Error renaming: $($file.Name)" -ForegroundColor Red
                    }
                }
                else {
                    # Remove duplicate/unnecessary file
                    try {
                        Remove-Item $file.FullName -Force
                        Write-Host "  Removed: $($file.Name)" -ForegroundColor Gray
                        $totalCleaned++
                    } catch {
                        Write-Host "  Error removing: $($file.Name)" -ForegroundColor Red
                    }
                }
            }
        }
    }
    
    # Show status
    $hasGameIcon = Test-Path (Join-Path $assetsImgPath "gameicon.png")
    $hasCurrency = Test-Path (Join-Path $assetsImgPath "game currency.png")
    $hasBackground = Test-Path (Join-Path $assetsImgPath "background.jpg")
    
    Write-Host "  Status: gameicon=$hasGameIcon currency=$hasCurrency background=$hasBackground" -ForegroundColor White
}

Write-Host "`nCleanup completed!" -ForegroundColor Green
Write-Host "Total operations: $totalCleaned" -ForegroundColor Cyan

# Final check
$cleanGames = 0
foreach ($folder in $gameFolders) {
    $remainingImages = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg" -or $_.Extension -eq ".webp" -or $_.Extension -eq ".bmp" -or $_.Extension -eq ".gif") -and
        $_.Name -notlike "*.html"
    }
    if ($remainingImages.Count -eq 0) { $cleanGames++ }
}

Write-Host "Games with clean main folders: $cleanGames/$($gameFolders.Count)" -ForegroundColor Green
