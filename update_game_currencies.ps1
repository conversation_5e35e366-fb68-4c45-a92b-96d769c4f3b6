# PowerShell Script to Update Game Currencies in All Files
# This script updates currency references in HTML and JS files

Write-Host "Updating game currencies in all files..." -ForegroundColor Green

# Define games and their currencies
$games = @{
    "Fortnite" = "V-Bucks"
    "PUBG Mobile" = "UC"
    "Free Fire" = "Diamonds"
    "DLS 25" = "Coins"
    "eFootball" = "eFootball Coins"
    "FC MOBILE" = "FC Points"
    "Roblox" = "Robux"
    "Monopoly GO" = "Dice Rolls"
    "Stumble Guys" = "Gems"
    "Royal Match" = "Coins"
    "Lords Mobile" = "Gems"
    "Township" = "Coins"
    "Bingo Blitz" = "Credits"
    "Homescapes" = "Coins"
    "Blood Strike" = "Diamonds"
    "BASEBALL 9" = "Coins"
    "Match Master" = "Coins"
    "Dragon City" = "Gems"
    "Candy Crush Saga" = "Gold Bars"
    "Mech Arena" = "A-Coins"
    "Cooking Fever" = "Coins"
    "Board Kings" = "Rolls"
    "ZEPETO" = "Coins"
    "DRAGON BALL LEGENDS" = "Chrono Crystals"
    "One Piece Bounty Rush" = "Rainbow Diamonds"
    "Subway Surfers" = "Coins"
    "Clash of Clans" = "Gems"
    "8 Ball Pool" = "Coins"
    "Pokemon Go" = "PokéCoins"
    "Car Parking" = "Money"
    "Car Parking Multiplayer 2" = "Money"
    "Chikii" = "Coins"
    "TopTop" = "Coins"
    "WePlay" = "Coins"
    "Jawaker" = "Chips"
    "Hay Day" = "Coins"
    "FROZEN CASH" = "Cash"
    "Yalla Ludo" = "Diamonds"
    "Ludo Star" = "Coins"
    "Ludo Club" = "Coins"
    "Blockman Go" = "Cubes"
    "CarX Street" = "Credits"
    "Capybara Go" = "Coins"
    "PK XD" = "Coins"
    "Zooba" = "Gems"
    "Family Island" = "Rubies"
    "Coin Master" = "Spins"
    "COD Mobile" = "CP"
    "Blooket" = "Tokens"
    "Hero Wars" = "Emeralds"
    "Hide Online" = "Coins"
    "HeadBall 2" = "Diamonds"
    "World of Tanks Blitz" = "Gold"
    "Uno" = "Coins"
    "BitLife" = "Bitizenship"
    "Avakin Life" = "Avacoins"
    "Tango" = "Coins"
    "HalaMe" = "Coins"
    "Sugo" = "Coins"
    "Bigo Live" = "Diamonds"
    "Poppo Live" = "Coins"
    "Tigo" = "Coins"
    "Cafe" = "Coins"
    "Starchat" = "Coins"
    "Soulchill" = "Diamonds"
    "Chamet" = "Diamonds"
    "Azar" = "Gems"
    "Top Follow" = "Coins"
    "Timo" = "Coins"
    "Golf Battle" = "Gems"
    "Last War Survival" = "Diamonds"
    "SimCity BuildIt" = "SimCash"
    "NBA Infinite" = "Coins"
    "FarmVille 2" = "Farm Bucks"
    "Brawl Stars" = "Gems"
}

foreach ($gameName in $games.Keys) {
    $currency = $games[$gameName]
    Write-Host "Processing: $gameName ($currency)" -ForegroundColor Yellow
    
    $gameFolder = $gameName
    if (!(Test-Path $gameFolder)) {
        Write-Host "  ⚠️  Folder not found: $gameFolder" -ForegroundColor Yellow
        continue
    }
    
    # Update main-fixed.js file
    $jsFile = Join-Path $gameFolder "assets\js\main-fixed.js"
    if (Test-Path $jsFile) {
        try {
            $jsContent = Get-Content $jsFile -Raw -Encoding UTF8
            
            # Replace currency references in JavaScript
            $jsContent = $jsContent -replace 'Gems', $currency
            $jsContent = $jsContent -replace '"7500"', '"7500"'  # Keep the number but update context
            $jsContent = $jsContent -replace 'Syncing \$\{targetGems\} Gems', "Syncing `${targetGems} $currency"
            $jsContent = $jsContent -replace 'synchronization of <span class="lsv2s">\$\{gems\}</span> <span class="lsv2s">Gems</span>', "synchronization of <span class=`"lsv2s`">`${gems}</span> <span class=`"lsv2s`">$currency</span>"
            
            # Save the updated content
            $jsContent | Out-File -FilePath $jsFile -Encoding UTF8 -NoNewline
            Write-Host "  ✅ Updated main-fixed.js" -ForegroundColor Green
        } catch {
            Write-Host "  ❌ Error updating main-fixed.js: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # Update index.html file
    $indexFile = Join-Path $gameFolder "index.html"
    if (Test-Path $indexFile) {
        try {
            $indexContent = Get-Content $indexFile -Raw -Encoding UTF8
            
            # Fix gameicon reference (change .jpg to .png if needed)
            $indexContent = $indexContent -replace 'gameicon\.jpg', 'gameicon.png'
            
            # Save the updated content
            $indexContent | Out-File -FilePath $indexFile -Encoding UTF8 -NoNewline
            Write-Host "  ✅ Updated index.html" -ForegroundColor Green
        } catch {
            Write-Host "  ❌ Error updating index.html: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # Update finalstep.html file
    $finalStepFile = Join-Path $gameFolder "finalstep.html"
    if (Test-Path $finalStepFile) {
        try {
            $finalStepContent = Get-Content $finalStepFile -Raw -Encoding UTF8
            
            # Fix gameicon reference
            $finalStepContent = $finalStepContent -replace 'gameicon\.jpg', 'gameicon.png'
            
            # Save the updated content
            $finalStepContent | Out-File -FilePath $finalStepFile -Encoding UTF8 -NoNewline
            Write-Host "  ✅ Updated finalstep.html" -ForegroundColor Green
        } catch {
            Write-Host "  ❌ Error updating finalstep.html: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host ""
}

Write-Host "Currency updates completed!" -ForegroundColor Green

# Show summary
Write-Host "`nSummary of updates:" -ForegroundColor Blue
Write-Host "- Updated currency references in main-fixed.js files" -ForegroundColor Cyan
Write-Host "- Fixed gameicon.jpg to gameicon.png in HTML files" -ForegroundColor Cyan
Write-Host "- Updated synchronization messages with correct currencies" -ForegroundColor Cyan
