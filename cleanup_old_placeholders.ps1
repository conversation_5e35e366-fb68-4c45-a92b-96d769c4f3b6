# PowerShell Script to Clean Up Old Placeholder Files
# This script removes old .jpg placeholder files when .png versions exist

Write-Host "Cleaning up old placeholder files..." -ForegroundColor Green

# Get all game directories
$gameFolders = Get-ChildItem -Directory | Where-Object { $_.Name -notlike "*.ps1" -and $_.Name -ne "*.html" }

foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    $pngFile = Join-Path $folder.FullName "$gameName.png"
    $jpgFile = Join-Path $folder.FullName "$gameName.jpg"
    
    # If PNG exists and JPG is a placeholder, remove the JPG
    if (Test-Path $pngFile) {
        if (Test-Path $jpgFile) {
            $jpgContent = Get-Content $jpgFile -Raw -ErrorAction SilentlyContinue
            if ($jpgContent -and $jpgContent.Contains("Placeholder")) {
                Remove-Item $jpgFile -Force
                Write-Host "✅ Removed placeholder: $gameName\$gameName.jpg" -ForegroundColor Green
            }
        }
    }
}

Write-Host "`nCleanup completed!" -ForegroundColor Green

# Show summary of PNG files
Write-Host "`nGames with PNG icons:" -ForegroundColor Cyan
foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    $pngFile = Join-Path $folder.FullName "$gameName.png"
    
    if (Test-Path $pngFile) {
        Write-Host "  ✅ $gameName" -ForegroundColor Green
    }
}
