# PowerShell Script to Organize All Images
# This script moves all images to the correct location in assets/img folder

Write-Host "Organizing all images to assets/img folders..." -ForegroundColor Green

# Get all game directories
$gameFolders = Get-ChildItem -Directory | Where-Object { 
    $_.Name -notlike "*.ps1" -and 
    $_.Name -ne "*.html" -and
    $_.Name -ne "*.md" -and
    (Test-Path (Join-Path $_.FullName "assets"))
}

$totalProcessed = 0
$totalMoved = 0

foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    Write-Host "`nProcessing: $gameName" -ForegroundColor Yellow
    
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    
    # Ensure assets/img folder exists
    if (!(Test-Path $assetsImgPath)) {
        New-Item -ItemType Directory -Path $assetsImgPath -Force | Out-Null
        Write-Host "  📁 Created assets/img folder" -ForegroundColor Cyan
    }
    
    # Get all image files in the game folder (excluding assets subfolder)
    $imageFiles = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg") -and
        $_.Name -notlike "*.html"
    }
    
    $gameIconSet = $false
    $currencySet = $false
    $backgroundSet = $false
    
    foreach ($imageFile in $imageFiles) {
        $fileName = $imageFile.Name
        $filePath = $imageFile.FullName
        
        Write-Host "  📷 Found: $fileName" -ForegroundColor Gray
        
        # Determine the type of image and set appropriate name
        $targetPath = ""
        
        # Check if it's a game icon
        if ($fileName -like "*$gameName*" -or 
            $fileName -like "*gameicon*" -or 
            $fileName -like "*icon*" -or
            $fileName -like "*logo*" -or
            ($fileName -like "*.png" -and !$gameIconSet)) {
            
            if (!$gameIconSet) {
                $targetPath = Join-Path $assetsImgPath "gameicon.png"
                $gameIconSet = $true
                Write-Host "    ➡️  Moving to gameicon.png" -ForegroundColor Green
            }
        }
        # Check if it's a currency image
        elseif ($fileName -like "*currency*" -or 
                $fileName -like "*coin*" -or 
                $fileName -like "*gem*" -or 
                $fileName -like "*diamond*" -or
                $fileName -like "*money*" -or
                $fileName -like "*token*" -or
                $fileName -like "*credit*" -or
                $fileName -like "*cash*" -or
                $fileName -like "*point*" -or
                $fileName -like "*buck*" -or
                $fileName -like "*robux*" -or
                $fileName -like "*uc*" -or
                $fileName -like "*cp*") {
            
            if (!$currencySet) {
                $targetPath = Join-Path $assetsImgPath "game currency.png"
                $currencySet = $true
                Write-Host "    ➡️  Moving to game currency.png" -ForegroundColor Magenta
            }
        }
        # Check if it's a background/wallpaper
        elseif ($fileName -like "*wallpaper*" -or 
                $fileName -like "*background*" -or 
                $fileName -like "*wall*" -or
                $fileName -like "*bg*" -or
                ($fileName -like "*.jpg" -and !$backgroundSet -and $fileName -notlike "*icon*")) {
            
            if (!$backgroundSet) {
                $targetPath = Join-Path $assetsImgPath "background.jpg"
                $backgroundSet = $true
                Write-Host "    ➡️  Moving to background.jpg" -ForegroundColor Blue
            }
        }
        # If we can't determine the type, try to guess based on what's missing
        else {
            if (!$gameIconSet -and ($fileName -like "*.png" -or $fileName -like "*$gameName*")) {
                $targetPath = Join-Path $assetsImgPath "gameicon.png"
                $gameIconSet = $true
                Write-Host "    ➡️  Guessing as gameicon.png" -ForegroundColor Yellow
            }
            elseif (!$currencySet) {
                $targetPath = Join-Path $assetsImgPath "game currency.png"
                $currencySet = $true
                Write-Host "    ➡️  Guessing as game currency.png" -ForegroundColor Yellow
            }
            elseif (!$backgroundSet) {
                $targetPath = Join-Path $assetsImgPath "background.jpg"
                $backgroundSet = $true
                Write-Host "    ➡️  Guessing as background.jpg" -ForegroundColor Yellow
            }
        }
        
        # Move the file if target path is determined
        if ($targetPath -ne "" -and !(Test-Path $targetPath)) {
            try {
                Copy-Item $filePath $targetPath -Force
                $totalMoved++
                Write-Host "    ✅ Moved successfully" -ForegroundColor Green
            } catch {
                Write-Host "    ❌ Error moving file: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
    
    # Clean up - remove original image files after copying
    foreach ($imageFile in $imageFiles) {
        $targetExists = $false
        
        # Check if the image was successfully copied to assets/img
        if ((Test-Path (Join-Path $assetsImgPath "gameicon.png")) -or
            (Test-Path (Join-Path $assetsImgPath "game currency.png")) -or
            (Test-Path (Join-Path $assetsImgPath "background.jpg"))) {
            
            try {
                Remove-Item $imageFile.FullName -Force
                Write-Host "  🗑️  Removed original: $($imageFile.Name)" -ForegroundColor Gray
            } catch {
                Write-Host "  ⚠️  Could not remove: $($imageFile.Name)" -ForegroundColor Yellow
            }
        }
    }
    
    $totalProcessed++
    
    # Show status for this game
    $hasGameIcon = Test-Path (Join-Path $assetsImgPath "gameicon.png")
    $hasCurrency = Test-Path (Join-Path $assetsImgPath "game currency.png")
    $hasBackground = Test-Path (Join-Path $assetsImgPath "background.jpg")
    
    Write-Host "  Status:" -ForegroundColor Cyan
    if ($hasGameIcon) { Write-Host "    ✅ gameicon.png" -ForegroundColor Green } else { Write-Host "    ❌ gameicon.png" -ForegroundColor Red }
    if ($hasCurrency) { Write-Host "    ✅ game currency.png" -ForegroundColor Green } else { Write-Host "    ❌ game currency.png" -ForegroundColor Red }
    if ($hasBackground) { Write-Host "    ✅ background.jpg" -ForegroundColor Green } else { Write-Host "    ❌ background.jpg" -ForegroundColor Red }
}

Write-Host "`n=== Final Summary ===" -ForegroundColor Magenta
Write-Host "Games processed: $totalProcessed" -ForegroundColor Cyan
Write-Host "Images moved: $totalMoved" -ForegroundColor Green

# Count complete games
$completeGames = 0
foreach ($folder in $gameFolders) {
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    $hasAll = (Test-Path (Join-Path $assetsImgPath "gameicon.png")) -and
              (Test-Path (Join-Path $assetsImgPath "game currency.png")) -and
              (Test-Path (Join-Path $assetsImgPath "background.jpg"))
    if ($hasAll) { $completeGames++ }
}

Write-Host "Games with complete image sets: $completeGames" -ForegroundColor Green
Write-Host "Completion rate: $([math]::Round(($completeGames / $totalProcessed) * 100, 1))%" -ForegroundColor Magenta

Write-Host "`n🎉 Image organization completed!" -ForegroundColor Green
