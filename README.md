# 🎮 مجموعة ألعاب منظمة - Organized Games Collection

## 📁 هيكل المجلدات

تم إنشاء **75 مجلد للألعاب** مع التنظيم التالي لكل لعبة:

```
اسم اللعبة/
├── اسم اللعبة.png          # أيقونة اللعبة الرئيسية
├── اسم العملة.jpg          # صورة عملة اللعبة
└── wallpaper اسم اللعبة.jpg # خلفية اللعبة
```

## 🔧 السكريبتات المتوفرة

### 1. `organize_icons.ps1`
- ينقل ملفات PNG إلى مجلداتها المناسبة
- يعيد تسمية الملفات حسب اسم اللعبة

### 2. `cleanup_old_placeholders.ps1`
- يحذف ملفات الـ .jpg القديمة عند وجود ملفات .png
- ينظف الملفات المؤقتة

### 3. `move_remaining_files.ps1`
- ينقل الملفات المتبقية إلى مجلداتها
- يتعامل مع الملفات ذات الأسماء المختصرة

## 🌐 صفحة البحث - games_search.html

### المميزات:
- **بحث سريع**: ابحث عن أي لعبة بالاسم
- **تصفية حسب الفئة**: أكشن، رياضة، ألغاز، اجتماعية
- **روابط بحث مباشرة**:
  - 🎮 بحث أيقونة اللعبة (512x512)
  - 💰 بحث صورة العملة
  - 🖼️ بحث خلفية اللعبة (4K)

### كيفية الاستخدام:
1. افتح ملف `games_search.html` في المتصفح
2. ابحث عن اللعبة المطلوبة
3. اضغط على الزر المناسب للبحث في Google Images

## 📊 إحصائيات

- **إجمالي الألعاب**: 75 لعبة
- **الألعاب مع أيقونات PNG**: 11 لعبة
- **إجمالي الملفات**: 225+ ملف

## 🎯 الألعاب المتوفرة

### ألعاب الأكشن
- Fortnite, PUBG Mobile, Free Fire
- Brawl Stars, COD Mobile, Clash of Clans
- Blood Strike, DRAGON BALL LEGENDS
- One Piece Bounty Rush, Subway Surfers
- Pokemon Go, Blockman Go, Zooba
- Hero Wars, Hide Online, World of Tanks Blitz
- Last War Survival, Mech Arena

### ألعاب الرياضة
- DLS 25, eFootball, FC MOBILE
- BASEBALL 9, 8 Ball Pool, Car Parking
- Car Parking Multiplayer 2, CarX Street
- HeadBall 2, Golf Battle, NBA Infinite

### ألعاب الألغاز
- Royal Match, Township, Bingo Blitz
- Homescapes, Match Master, Dragon City
- Candy Crush Saga, Cooking Fever
- Board Kings, Jawaker, Hay Day
- FROZEN CASH, Yalla Ludo, Ludo Star
- Ludo Club, Capybara Go, Family Island
- Coin Master, Blooket, Uno
- SimCity BuildIt, FarmVille 2, Monopoly GO

### ألعاب اجتماعية
- Roblox, ZEPETO, Chikii, TopTop
- WePlay, BitLife, Avakin Life
- Tango, HalaMe, Sugo, Bigo Live
- Poppo Live, Tigo, Cafe, Starchat
- Soulchill, Chamet, Azar
- Top Follow, Timo, PK XD

## 🌐 صفحات الهبوط - Landing Pages

### تم إنشاء صفحات هبوط مخصصة لكل لعبة:

#### هيكل صفحة الهبوط:
```
اسم اللعبة/
├── index.html              # الصفحة الرئيسية
├── finalstep.html          # صفحة الخطوة الأخيرة
└── assets/
    ├── css/               # ملفات التنسيق
    ├── js/                # ملفات JavaScript
    ├── sound/             # الأصوات
    └── img/
        ├── gameicon.png       # أيقونة اللعبة
        ├── game currency.png  # صورة العملة
        ├── background.jpg     # خلفية اللعبة
        ├── favicon.ico        # أيقونة المتصفح
        └── mgi.png           # صور إضافية
```

#### المميزات:
- **صفحات مخصصة**: كل لعبة لها صفحة هبوط مخصصة
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **أسماء موحدة للصور**:
  - `gameicon.png` - أيقونة اللعبة
  - `game currency.png` - صورة العملة
  - `background.jpg` - خلفية اللعبة

### السكريبتات الجديدة:

#### `create_landing_pages.ps1`
- ينسخ بنية صفحة الهبوط من Brawl Stars
- يخصص المحتوى لكل لعبة
- ينشئ ملفات HTML مخصصة

#### `rename_images.ps1`
- يعيد تسمية الصور حسب الاتفاقية الجديدة
- ينقل الصور إلى مجلد assets/img

#### `complete_image_setup.ps1`
- يكمل إعداد الصور المفقودة
- يضمن وجود جميع الصور المطلوبة

#### `landing_pages_summary.ps1`
- يعرض إحصائيات شاملة لصفحات الهبوط
- يظهر حالة كل لعبة

## 📝 ملاحظات

- **إجمالي الألعاب**: 75 لعبة
- **صفحات الهبوط**: تم إنشاؤها لجميع الألعاب
- **الصور**: تم توحيد أسماء الصور حسب الاتفاقية الجديدة

## 🚀 للمطورين

### إنشاء صفحات هبوط جديدة:
```powershell
.\create_landing_pages.ps1
```

### إعادة تسمية الصور:
```powershell
.\rename_images.ps1
```

### إكمال إعداد الصور:
```powershell
.\complete_image_setup.ps1
```

### عرض ملخص صفحات الهبوط:
```powershell
.\landing_pages_summary.ps1
```

### إضافة لعبة جديدة:
```powershell
.\add_new_game.ps1 -GameName "اسم اللعبة" -Currency "اسم العملة"
```
