# PowerShell Script for Manual Cleanup of All Games
# This script manually organizes images in each game folder

Write-Host "Starting manual cleanup of all game folders..." -ForegroundColor Green

# Get all game directories
$gameFolders = Get-ChildItem -Directory | Where-Object { 
    $_.Name -notlike "*.ps1" -and 
    $_.Name -ne "*.html" -and
    $_.Name -ne "*.md" -and
    $_.Name -ne "Example Game"
}

$totalProcessed = 0
$totalCleaned = 0

foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    Write-Host "`n📁 Processing: $gameName" -ForegroundColor Yellow
    
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    
    # Ensure assets/img folder exists
    if (!(Test-Path $assetsImgPath)) {
        New-Item -ItemType Directory -Path $assetsImgPath -Force | Out-Null
        Write-Host "  📂 Created assets/img folder" -ForegroundColor Cyan
    }
    
    # Get all image files in the main folder
    $imageFiles = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg" -or $_.Extension -eq ".webp" -or $_.Extension -eq ".bmp" -or $_.Extension -eq ".gif") -and
        $_.Name -notlike "*.html"
    }
    
    if ($imageFiles.Count -eq 0) {
        Write-Host "  ✅ No images to clean in main folder" -ForegroundColor Green
        $totalProcessed++
        continue
    }
    
    Write-Host "  📷 Found $($imageFiles.Count) images to organize" -ForegroundColor Cyan
    
    # Check what we already have in assets/img
    $hasGameIcon = Test-Path (Join-Path $assetsImgPath "gameicon.png")
    $hasCurrency = Test-Path (Join-Path $assetsImgPath "game currency.png")
    $hasBackground = Test-Path (Join-Path $assetsImgPath "background.jpg")
    
    foreach ($imageFile in $imageFiles) {
        $fileName = $imageFile.Name
        $fileBaseName = $imageFile.BaseName
        $filePath = $imageFile.FullName
        
        Write-Host "    📸 Processing: $fileName" -ForegroundColor Gray
        
        $action = "skip"
        $targetPath = ""
        
        # Determine what to do with this image
        # Priority 1: Game icon
        if (($fileName -like "*$gameName*" -or $fileName -like "*gameicon*" -or $fileName -like "*icon*" -or $fileName -like "*logo*") -and !$hasGameIcon) {
            $targetPath = Join-Path $assetsImgPath "gameicon.png"
            $action = "move"
            $hasGameIcon = $true
            Write-Host "      ➡️  → gameicon.png" -ForegroundColor Green
        }
        # Priority 2: Currency
        elseif (($fileName -like "*currency*" -or $fileName -like "*coin*" -or $fileName -like "*gem*" -or $fileName -like "*diamond*" -or $fileName -like "*money*" -or $fileName -like "*token*" -or $fileName -like "*credit*" -or $fileName -like "*cash*" -or $fileName -like "*point*" -or $fileName -like "*buck*" -or $fileName -like "*robux*" -or $fileName -like "*uc*" -or $fileName -like "*cp*") -and !$hasCurrency) {
            $targetPath = Join-Path $assetsImgPath "game currency.png"
            $action = "move"
            $hasCurrency = $true
            Write-Host "      ➡️  → game currency.png" -ForegroundColor Magenta
        }
        # Priority 3: Background
        elseif (($fileName -like "*wallpaper*" -or $fileName -like "*background*" -or $fileName -like "*wall*" -or $fileName -like "*bg*" -or $fileName -like "*images*") -and !$hasBackground) {
            $targetPath = Join-Path $assetsImgPath "background.jpg"
            $action = "move"
            $hasBackground = $true
            Write-Host "      ➡️  → background.jpg" -ForegroundColor Blue
        }
        # If we already have all three, or it's unclear what this image is
        else {
            $action = "delete"
            Write-Host "      🗑️  → DELETE (not needed)" -ForegroundColor Red
        }
        
        # Execute the action
        try {
            if ($action -eq "move") {
                Move-Item $filePath $targetPath -Force
                Write-Host "      ✅ Moved successfully" -ForegroundColor Green
                $totalCleaned++
            } elseif ($action -eq "delete") {
                Remove-Item $filePath -Force
                Write-Host "      ✅ Deleted successfully" -ForegroundColor Gray
                $totalCleaned++
            }
        } catch {
            Write-Host "      ❌ Error: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    $totalProcessed++
    
    # Show final status
    $finalGameIcon = Test-Path (Join-Path $assetsImgPath "gameicon.png")
    $finalCurrency = Test-Path (Join-Path $assetsImgPath "game currency.png")
    $finalBackground = Test-Path (Join-Path $assetsImgPath "background.jpg")
    
    Write-Host "  📊 Final Status:" -ForegroundColor Cyan
    if ($finalGameIcon) { Write-Host "    ✅ gameicon.png" -ForegroundColor Green } else { Write-Host "    ❌ gameicon.png" -ForegroundColor Red }
    if ($finalCurrency) { Write-Host "    ✅ game currency.png" -ForegroundColor Green } else { Write-Host "    ❌ game currency.png" -ForegroundColor Red }
    if ($finalBackground) { Write-Host "    ✅ background.jpg" -ForegroundColor Green } else { Write-Host "    ❌ background.jpg" -ForegroundColor Red }
    
    # Check if main folder is now clean
    $remainingImages = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg" -or $_.Extension -eq ".webp" -or $_.Extension -eq ".bmp" -or $_.Extension -eq ".gif") -and
        $_.Name -notlike "*.html"
    }
    
    if ($remainingImages.Count -eq 0) {
        Write-Host "    🎉 Main folder is clean!" -ForegroundColor Green
    } else {
        Write-Host "    ⚠️  $($remainingImages.Count) images still in main folder" -ForegroundColor Yellow
    }
}

Write-Host "`n=== Final Summary ===" -ForegroundColor Magenta
Write-Host "Games processed: $totalProcessed" -ForegroundColor Cyan
Write-Host "Images moved/deleted: $totalCleaned" -ForegroundColor Green

# Final verification
$cleanGames = 0
$completeGames = 0

foreach ($folder in $gameFolders) {
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    
    # Check if main folder is clean
    $remainingImages = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg" -or $_.Extension -eq ".webp" -or $_.Extension -eq ".bmp" -or $_.Extension -eq ".gif") -and
        $_.Name -notlike "*.html"
    }
    
    if ($remainingImages.Count -eq 0) { $cleanGames++ }
    
    # Check if complete
    $hasAll = (Test-Path (Join-Path $assetsImgPath "gameicon.png")) -and
              (Test-Path (Join-Path $assetsImgPath "game currency.png")) -and
              (Test-Path (Join-Path $assetsImgPath "background.jpg"))
    
    if ($hasAll) { $completeGames++ }
}

$cleanRate = [math]::Round(($cleanGames / $totalProcessed) * 100, 1)
$completeRate = [math]::Round(($completeGames / $totalProcessed) * 100, 1)

Write-Host "`nGames with clean main folders: $cleanGames/$totalProcessed ($cleanRate%)" -ForegroundColor Green
Write-Host "Games with complete image sets: $completeGames/$totalProcessed ($completeRate%)" -ForegroundColor Green

if ($cleanRate -eq 100) {
    Write-Host "`n🎉 SUCCESS! All main folders are clean!" -ForegroundColor Green
    Write-Host "All images are now properly organized in assets/img folders!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  Some games may need additional manual attention" -ForegroundColor Yellow
}

Write-Host "`n📋 Manual cleanup completed!" -ForegroundColor Blue
