﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
	
<!-- Mirrored from www.apkmoode.com/brawlstars/ by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 01 Jul 2025 13:46:39 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
		<title>Last War Survival Free Diamonds </title>
		<meta name="viewport" content="height=device-height, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, target-densitydpi=device-dpi">
		<meta name="description" content="Discover the Last War Survival Generator tool for unlimited free resources! Generate Diamonds and gold effortlessly to dominate Last War Survival without spending a dime. Explore the powerful Last War Survival Generator and enhance your gaming experience today!" />    
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<link rel="icon" type="image/ico" href="assets/img/favicon.ico" />
		<!-- Open Graph Meta Tags-->
		<meta property="og:title" content="Last War Survival Free Diamonds " /> <!-- Title which is displayed when your site is shared on social networks -->
		<meta property="og:description" content="Discover the Last War Survival Generator tool for unlimited free resources! Generate Diamonds and gold effortlessly to dominate Last War Survival without spending a dime. Explore the powerful Last War Survival Generator and enhance your gaming experience today!" /> <!-- Website description which is displayed when your site is shared on social networks -->
		<meta property="og:type" content="website" />
		<meta property="og:url" content="index.html" /> <!-- Your Website URL -->
		<!-- Twitter Meta -->
		<meta name="twitter:card" content="summary" />
		<meta name="twitter:title" content="Last War Survival Free Diamonds " />
		<meta name="twitter:description" content="Discover the Last War Survival Generator tool for unlimited free resources! Generate Diamonds and gold effortlessly to dominate Last War Survival without spending a dime. Explore the powerful Last War Survival Generator and enhance your gaming experience today!" />
		<!-- Icons -->
		<link rel="stylesheet" href="https://cdn.linearicons.com/free/1.0.0/icon-font.min.css">
		<link href="https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Two+Tone|" rel="stylesheet">
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css" integrity="sha512-+4zCK9k+qNFUR5X+cKL9EIR+ZOhtIloNl9GIKS57V1MyNsYpYcUrUeQc9vNfzsWfV28IaLL3i96P9sdNyeRssA==" crossorigin="anonymous" />
		<!-- Google Fonts -->
		<link rel="preconnect" href="https://fonts.gstatic.com/">
		<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&amp;display=swap" rel="stylesheet">
		<!-- CSS -->
		<link href="assets/css/bootstrap.min.css" rel="stylesheet" />  
		<link href="assets/css/animate.min.css" rel="stylesheet" />
		<link href="assets/css/style.css" rel="stylesheet" />	
		<link href="assets/css/a-c-c4.css" rel="stylesheet" />			<!-- Global site tag (gtag.js) - Google Analytics -->
			<script async src="https://www.googletagmanager.com/gtag/js?id=G-TY1PNZCPG9"></script>
			<script>
			  window.dataLayer = window.dataLayer || [];
			  function gtag(){dataLayer.push(arguments);}
			  gtag('js', new Date());			  	
			  gtag('config', 'G-TY1PNZCPG9');
			</script>
			</head>
	<body>	
	<div class="prlp">	
		<div class="prlw">	
			<div class="prlw-r"></div>
			<div class="prlw-l"></div>
		</div>
		<div class="prlc">
			<div class="prlc-i">
				<div class="prlc-l animate__animated animate__bounceIn animation-delay-300"><span class="material-icons-two-tone fa-spin">settings</span></div>
				<div class="prlc-lb animate__animated animate__bounceIn animation-delay-400"><div></div></div>
			</div>
		</div>
	</div>
	<header>
				<div class="container">
			<div class="h-c">
				<img src="assets/img/gameicon.png" class="img-fluid l-i" />				<h1>Last War Survival Diamonds Generator</h1>				<p>#1 Last War Survival Free Diamonds</p>			</div>
		</div>	
		<div id="header-particles"></div>	</header>
	<section class="m-s">
				<div class="container">
			<div class="c-w c-w-u aoi aoinv">
				<div class="s-o-w">
					<div class="s-o aoi aoinv animation-delay-100">
						<span>1</span>
					</div>
				</div>
				<div class="b-s-c-w">
					<div class="c-i-t aoi aoinv animation-delay-200">
						<span class="c-i-t-v">Please enter your username and select your platform.</span>
					</div>
					<div class="u-f-w">
						<div id="u-f" class="aoi aoinv animation-delay-300">
							<div class="u-i-w">
								<span class="material-icons-two-tone">face</span>
								<input id="u-i" class="u-i" placeholder="Your Username..." />
							</div>
						</div>
						<div class="s-e-w s-e-w-u">
							<div class="s-e-w-i">
								<span>Please enter your username.</span>
							</div>
						</div>
					</div>
					<div class="p-f-w aoi aoinv animation-delay-400">
						<div class="p-f-i">
																					<div class="p-s-i-w">
								<div class="p-s-i p-s-i-4">
									<i class="fab fa-android"></i>
								</div>
							</div>
														<div class="p-s-i-w">
								<div class="p-s-i p-s-i-5">
									<i class="fab fa-apple"></i>
								</div>
							</div>
													</div>
						<div class="s-e-w s-e-w-p">
							<div class="s-e-w-i">
								<span>Please select your platform.</span>
							</div>
						</div>
					</div>
					<div class="p-b-w aoi aoinv animation-delay-500">
						<div id="p-b-a" class="p-b"><span>Proceed</span></div>
					</div>
				</div>
				<div class="a-s-c-w"></div>
			</div>				
		</div>
	</section>
		<div class="bg-o" style="background-image: url('assets/img/background.jpg');"></div>
</body>	<!-- JS -->
	<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.4/jquery.min.js"></script>
	<script type="text/javascript" src="assets/js/jquery.countTo.js"></script>
	<script type="text/javascript">var s_s;</script>

	<script type="text/javascript" src="assets/js/ion.sound.min.js"></script>
	<script type="text/javascript">s_s = '1';</script>

		<script type="text/javascript" src="assets/js/particles.min.js"></script>
		<script type="text/javascript" src="assets/js/main-fixed.js"></script>
<!-- Mirrored from www.apkmoode.com/brawlstars/ by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 01 Jul 2025 13:46:47 GMT -->
</html>
