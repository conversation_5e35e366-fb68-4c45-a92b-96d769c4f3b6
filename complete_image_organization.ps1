# PowerShell Script to Complete Image Organization
# This script ensures all games have the required images in assets/img

Write-Host "Completing image organization..." -ForegroundColor Green

# Get all game directories
$gameFolders = Get-ChildItem -Directory | Where-Object { 
    $_.Name -notlike "*.ps1" -and 
    $_.Name -ne "*.html" -and
    $_.Name -ne "*.md" -and
    (Test-Path (Join-Path $_.FullName "assets"))
}

foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    Write-Host "`nProcessing: $gameName" -ForegroundColor Yellow
    
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    
    # Check what's missing
    $gameIconPath = Join-Path $assetsImgPath "gameicon.png"
    $currencyPath = Join-Path $assetsImgPath "game currency.png"
    $backgroundPath = Join-Path $assetsImgPath "background.jpg"
    
    $hasGameIcon = Test-Path $gameIconPath
    $hasCurrency = Test-Path $currencyPath
    $hasBackground = Test-Path $backgroundPath
    
    # Look for images in the main folder
    $imageFiles = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg") -and
        $_.Name -notlike "*.html"
    }
    
    # Try to fill missing images
    if (!$hasGameIcon) {
        # Look for game icon
        $iconCandidates = $imageFiles | Where-Object { 
            $_.Name -like "*$gameName*" -or 
            $_.Name -like "*icon*" -or 
            $_.Name -like "*logo*" -or
            $_.Extension -eq ".png"
        }
        
        if ($iconCandidates.Count -gt 0) {
            $iconFile = $iconCandidates[0]
            try {
                Copy-Item $iconFile.FullName $gameIconPath -Force
                Write-Host "  ✅ Added gameicon.png from $($iconFile.Name)" -ForegroundColor Green
                $hasGameIcon = $true
            } catch {
                Write-Host "  ❌ Failed to copy game icon" -ForegroundColor Red
            }
        }
    }
    
    if (!$hasCurrency) {
        # Look for currency image
        $currencyCandidates = $imageFiles | Where-Object { 
            $_.Name -like "*currency*" -or 
            $_.Name -like "*coin*" -or 
            $_.Name -like "*gem*" -or 
            $_.Name -like "*diamond*" -or
            $_.Name -like "*money*" -or
            $_.Name -like "*token*" -or
            $_.Name -like "*credit*" -or
            $_.Name -like "*cash*" -or
            $_.Name -like "*point*" -or
            $_.Name -like "*buck*" -or
            $_.Name -like "*robux*" -or
            $_.Name -like "*uc*" -or
            $_.Name -like "*cp*" -or
            ($_.Name -notlike "*$gameName*" -and $_.Name -notlike "*wallpaper*" -and $_.Name -notlike "*background*")
        }
        
        if ($currencyCandidates.Count -gt 0) {
            $currencyFile = $currencyCandidates[0]
            try {
                Copy-Item $currencyFile.FullName $currencyPath -Force
                Write-Host "  ✅ Added game currency.png from $($currencyFile.Name)" -ForegroundColor Magenta
                $hasCurrency = $true
            } catch {
                Write-Host "  ❌ Failed to copy currency image" -ForegroundColor Red
            }
        }
    }
    
    if (!$hasBackground) {
        # Look for background image
        $backgroundCandidates = $imageFiles | Where-Object { 
            $_.Name -like "*wallpaper*" -or 
            $_.Name -like "*background*" -or 
            $_.Name -like "*wall*" -or
            $_.Name -like "*bg*" -or
            ($_.Extension -eq ".jpg" -and $_.Name -notlike "*icon*" -and $_.Name -notlike "*currency*")
        }
        
        if ($backgroundCandidates.Count -gt 0) {
            $backgroundFile = $backgroundCandidates[0]
            try {
                Copy-Item $backgroundFile.FullName $backgroundPath -Force
                Write-Host "  ✅ Added background.jpg from $($backgroundFile.Name)" -ForegroundColor Blue
                $hasBackground = $true
            } catch {
                Write-Host "  ❌ Failed to copy background image" -ForegroundColor Red
            }
        }
    }
    
    # Clean up original files if all images are now in place
    if ($hasGameIcon -and $hasCurrency -and $hasBackground) {
        foreach ($imageFile in $imageFiles) {
            try {
                Remove-Item $imageFile.FullName -Force
                Write-Host "  🗑️  Removed: $($imageFile.Name)" -ForegroundColor Gray
            } catch {
                Write-Host "  ⚠️  Could not remove: $($imageFile.Name)" -ForegroundColor Yellow
            }
        }
    }
    
    # Show final status
    Write-Host "  Final Status:" -ForegroundColor Cyan
    if (Test-Path $gameIconPath) { Write-Host "    ✅ gameicon.png" -ForegroundColor Green } else { Write-Host "    ❌ gameicon.png" -ForegroundColor Red }
    if (Test-Path $currencyPath) { Write-Host "    ✅ game currency.png" -ForegroundColor Green } else { Write-Host "    ❌ game currency.png" -ForegroundColor Red }
    if (Test-Path $backgroundPath) { Write-Host "    ✅ background.jpg" -ForegroundColor Green } else { Write-Host "    ❌ background.jpg" -ForegroundColor Red }
}

# Clean up root level images
Write-Host "`nCleaning up root level images..." -ForegroundColor Blue
$rootImages = Get-ChildItem . -File | Where-Object { 
    ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg") -and
    $_.Name -notlike "*.html"
}

foreach ($rootImage in $rootImages) {
    Write-Host "  🗑️  Removing root image: $($rootImage.Name)" -ForegroundColor Gray
    try {
        Remove-Item $rootImage.FullName -Force
    } catch {
        Write-Host "  ⚠️  Could not remove: $($rootImage.Name)" -ForegroundColor Yellow
    }
}

Write-Host "`n🎉 Image organization completed!" -ForegroundColor Green
