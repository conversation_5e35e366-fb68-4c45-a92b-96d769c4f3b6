/*--------------------------------------------------------------
# Basics
--------------------------------------------------------------*/
html {
	overflow-y: scroll;
	overflow-x: hidden;
}
body {
	overflow-x: hidden;
	min-height: 100vh;
}
ul {
	list-style: none;
	margin: 0;
	padding: 0;
}
textarea, input, a, button { 
	outline: none;
}

/*--------------------------------------------------------------
# Typography
--------------------------------------------------------------*/
body {
	font-family: 'Roboto', sans-serif;
	font-size: 16px;
	color: #393b3d;
}
h1, h2, h3, h4, h5, h6 {
	font-weight: 700;
}
a {
	-webkit-transition: all 0.2s ease-in-out 0s;
	-moz-transition: all 0.2s ease-in-out 0s;
	-ms-transition: all 0.2s ease-in-out 0s;
	-o-transition: all 0.2s ease-in-out 0s;
	transition: all 0.2s ease-in-out 0s;
}
a:hover, a:visited, a:focus, a:active, button:hover, button:visited, button:active, button:focus; {
	text-decoration: none !important;
	outline: none !important;
}
::selection {
	background: #000;
	color: #fff;
	text-shadow: none;
}
::-moz-selection {
	background: #000;
	color: #fff;
	text-shadow: none;
}
.animation-delay-100 {
	-webkit-animation-delay: 0.1s;
	animation-delay: 0.1s;
}
.animation-delay-200 {
	-webkit-animation-delay: 0.2s;
	animation-delay: 0.2s;
}
.animation-delay-300 {
	-webkit-animation-delay: 0.3s;
	animation-delay: 0.3s;
}
.animation-delay-400 {
	-webkit-animation-delay: 0.4s;
	animation-delay: 0.4s;
}
.animation-delay-500 {
	-webkit-animation-delay: 0.5s;
	animation-delay: 0.5s;
}
.animation-delay-600 {
	-webkit-animation-delay: 0.6s;
	animation-delay: 0.6s;
}
.animation-delay-700 {
	-webkit-animation-delay: 0.7s;
	animation-delay: 0.7s;
}
.animation-delay-800 {
	-webkit-animation-delay: 0.8s;
	animation-delay: 0.8s;
}
.animation-delay-1000 {
	-webkit-animation-delay: 1s;
	animation-delay: 1s;
}
.animation-delay-2000 {
	-webkit-animation-delay: 2s;
	animation-delay: 2s;
}
.aoinv {
	opacity: 0;
}
.bg-o {
	position: fixed;
	height: 100vh;
	width: 100%;
	left: 0;
	bottom: 0;
	background-repeat: no-repeat !important;
	background-size: cover !important;
	-webkit-background-size: cover !important;
	background-position: center bottom !important;
	will-change: transform;
	-webkit-backface-visibility: hidden;
	transform: translate3d(0, 0, 0);
}
header {
	min-height: 150px;
	padding: 15px 0;
	background: #fff;
	box-shadow: 0 3px 7px 0 rgba(0, 0, 0, 0.4);
	position: relative;
	z-index: 2;
	display: flex;
	align-items: center;
	justify-content: center;
}
#header-particles {
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 1;
	left: 0;
	top: 0;
}
.h-c {
	text-align: center;
}
.h-c .l-i {
	max-width: 350px;
	max-height: 60px;
	display: table;
	margin: 0 auto;
}
.h-c h1 {
	font-weight: 900;
	margin: 5px auto 0 auto;
	text-transform: uppercase;
    letter-spacing: 5px;
    font-size: 0.8em;
}
.h-c p {
	font-weight: 300;
	font-size: 1.2em;
	margin: 5px 0 0 0;
}
.m-s {
	min-height: calc(100vh - 150px);
	padding: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
}
.m-s .container {
	max-width: 720px;
	margin: 0 auto;
	position: relative;
	z-index: 2;
}
.c-w {
	position: relative;
	background: #fff;
	box-shadow: 0 0 0 7px rgba(0, 0, 0, 0.3);
	border-radius: 7px;
	padding: 70px 120px 50px 120px;
}
.s-o-w {
	position: absolute;
	top: -50px;
	width: 100%;
	left: 0;
}
.s-o {
	width: 100px;
	height: 100px;
	border-radius: 50%;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto;
	border: 5px solid #fff;
	box-shadow: inset 0 3px 5px 0 rgba(0, 0, 0, 0.15);
}
.s-o span {
	color: #fff;
	font-size: 3em;
	font-weight: 700;
	text-shadow: 3px 3px 0 rgba(0, 0, 0, 0.3);
}
.c-i-t {
	margin-bottom: 30px;
}
.c-i-t span.c-i-t-v {
	font-size: 1.4em;
	text-align: center;
	display: block;
	font-weight: 300;
}
.u-f-w, .p-f-w {
	position: relative;
}
.u-i-w {
	position: relative;
	background: #eee;
	border-bottom: 2px solid #e5e5e5;
	border-radius: 7px;
}
.u-i-w span.material-icons-two-tone {
	position: absolute;
	font-size: 2em;
	left: 22px;
	top: 30px;
}
.u-i {
	outline: none;
	appearance: none;
	-moz-appearance: none;
	-webkit-appearance: none;
	background: transparent;
	box-shadow: none;
	border: none;
	width: 100%;
	height: 90px;
	padding: 0 15px 0 70px;
	color: #000;
	font-size: 1.4em;
	font-weight: 700;
}
.u-i::placeholder {
	color: #000;
	opacity: 1;
}
.u-i:-ms-input-placeholder {
	color: #000;
}
.u-i::-ms-input-placeholder {
	color: #000;
}
.p-f-w {
	margin: 40px -5px 0 -5px;
}
.p-f-i {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
}
.p-s-i-w {
	padding: 0 5px;
	flex: 1;
}
.p-s-i {
	position: relative;
	background: #eee;
	box-shadow: inset 0 -2px 0 0 #e5e5e5;
	border-radius: 7px;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 90px;
	padding: 0 5px;
	cursor: pointer;
}
.p-s-i i {
	color: #000;
	font-size: 2em;
}
.p-s-i.active i {
	color: #fff;
}
.p-b-w {
	margin: 50px auto 0 auto;
}
.p-b {
	position: relative;
	height: 90px;
	max-width: 280px;
	margin: 0 auto;
	padding: 5px 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	color: #fff;
	text-shadow: 2px 2px 0 rgba(0, 0, 0, 0.3);
	font-weight: 700;
	font-size: 1.6em;
	letter-spacing: 1px;
	border-radius: 7px;
	box-shadow: inset 0 -5px 0 0 rgba(0, 0, 0, 0.3);
}
.p-b:active {
	top: 3px;
	box-shadow: inset 0 -2px 0 0 rgba(0, 0, 0, 0.3);
}
.s-e-w {
	text-align: center;
	position: absolute;
	width: 100%;
	left: 0;
	bottom: -28px;
	display: none;
}
.s-e-w-i {
	background: #fc4349;
	display: table;
	margin: 0 auto;
	padding: 2px 10px;
	color: #fff;
	letter-spacing: 1px;
	font-size: 0.8em;
	border-radius: 3px;
}
.s-e-w-p {
	padding: 0 5px;
}
.a-s-c-w {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
}
.s-f-p-i {
	position: relative;
}
.s-f-p-t-l {
	font-size: 1.4em;
	text-align: center;
	text-transform: uppercase;
	letter-spacing: 2px;
	font-weight: 700;
	margin-bottom: 10px;
}
.s-f-p-t-u-w {
	margin: 0 auto;
	width: 360px;
	text-align: center;
	padding: 0 5px;
	height: 100px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 7px;
	box-shadow: inset 0 0 0 5px rgba(0, 0, 0, 0.3);
}
.s-f-p-t-u-v {
	font-weight: 700;
	font-size: 2em;
	color: #fff;
	text-shadow: 2px 2px 0 rgba(0, 0, 0, 0.15);
}
.s-f-p-t-p-w {
	width: 100px;
	text-align: center;
	padding: 0 5px;
	height: 100px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 7px;
	margin: 20px auto 0 auto;
	font-size: 1.6em;
	color: #fff;
	box-shadow: inset 0 0 0 5px rgba(0, 0, 0, 0.3);
}
.s-f-p-a-w {
	position: absolute;
	top: 20%;
	left: -5%;
	width: 100%;
	text-align: center;
}
.s-f-p-a-w img {
	margin: 0 auto;
	display: table;
}
.ssa {
	-webkit-animation: ssa 2s linear infinite;
	animation: ssa 2s linear infinite
}
@-webkit-keyframes ssa {
	from {
		-webkit-transform: rotate(0) translateX(20%) rotate(0);
		transform: rotate(0) translateX(20%) rotate(0)
	}
	to {
		-webkit-transform: rotate(360deg) translateX(20%) rotate(-360deg);
		transform: rotate(360deg) translateX(20%) rotate(-360deg)
	}
}
@keyframes ssa {
	from {
		-webkit-transform: rotate(0) translateX(20%) rotate(0);
		transform: rotate(0) translateX(20%) rotate(0)
	}
	to {
		-webkit-transform: rotate(360deg) translateX(20%) rotate(-360deg);
		transform: rotate(360deg) translateX(20%) rotate(-360deg)
	}
}
.caSs-cm-ci {
	stroke-dasharray: 320;
	stroke-dashoffset: 320;
	stroke-width: 2;
	stroke-miterlimit: 10;
	stroke: #56ab54;
	fill: none;
	animation: caSt 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}
.caSs-cm {
	width: 168px;
	height: 168px;
	border-radius: 50%;
	display: block;
	stroke-width: 4;
	stroke: #fff;
	stroke-miterlimit: 10;
	margin: 10% auto;
	box-shadow: inset 0px 0px 0px #83dd81;
	animation: caF .4s ease-in-out .4s forwards, caSc .3s ease-in-out .9s both;
}
.caSs-cm-ch {
	transform-origin: 50% 50%;
	stroke-dasharray: 146;
	stroke-dashoffset: 146;
	animation: caSt 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}
@-webkit-keyframes caSt {
	100% {
		stroke-dashoffset: 0;
	}
}
@-webkit-keyframes caSc {
	0%, 100% {
		transform: none;
	}
	50% {
		transform: scale3d(1.1, 1.1, 1);
	}
}
@-webkit-keyframes caF {
	100% {
		box-shadow: inset 0px 0px 0px 100px #83dd81;
	}
}
@keyframes caSt {
	100% {
		stroke-dashoffset: 0;
	}
}
@keyframes caSc {
	0%, 100% {
		transform: none;
	}
	50% {
		transform: scale3d(1.1, 1.1, 1);
	}
}
@keyframes caF {
	100% {
		box-shadow: inset 0px 0px 0px 100px #83dd81;
	}
}
.caSs-ts {
	color: #83dd81;
}
.c-w-r {
	max-width: 400px;
	margin: 0 auto;
	padding: 70px 70px 50px 70px;
}
.r-i-s-i-w {
	background: #eee;
	border-bottom: 2px solid #e5e5e5;
	border-radius: 7px;
	padding: 20px 25px 10px 25px;
	margin: 0 auto;
	position: relative;
	text-align: center;
}
.r-i-s-r-h-w {
	position: absolute;
	top: -12px;
	left: 0;
	width: 100%;
}
.r-i-s-r-h-w span {
	display: table;
	margin: 0 auto;
	padding: 7px 10px;
	line-height: 1;
	color: #fff;
	font-weight: 700;
	box-shadow: inset 0 -2px 0 0 rgba(0, 0, 0, 0.25);
	text-transform: uppercase;
	font-size: 0.7em;	
	border-radius: 5px;
}
.r-i-s-r-w {
	text-align: center;
}
.r-i-s-r-m-w {
	display: flex;
	align-items: center;
	justify-content: center;
}
.r-i-s-r-w-i {
	max-width: 70px;
	max-height: 70px;
	margin-bottom: 10px;
}
.r-i-s-r-m-v {
	font-size: 2.6em;
	font-weight: 900;
	margin-right: 7px;
	line-height: 1;
	animation-duration: 0.5s;
}
.r-i-s-r-m-l {
	font-size: 1.4em;
	font-weight: 300;
	line-height: 1;
}
.r-s-i-w {
	margin-top: 20px;
}
.r-s-i {
	border: 2px solid #333;
	border-radius: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	line-height: 1;
	padding: 10px 5px 8px 5px;
	margin-bottom: 10px;
	cursor: pointer;
	-webkit-transition: all 300ms cubic-bezier(0.225, 3, 0.485, 0.895);
	transition: all 300ms cubic-bezier(0.225, 3, 0.485, 0.895);
}
.r-s-i:last-child {
	margin-bottom: 0;
}
.r-s-i-v {
	font-weight: 700;
	margin-right: 7px;
	font-size: 1.2em;
}
.r-s-i-l {
	line-height: 1;
	position: relative;
	top: 1px;
}
.r-s-i-i{
	margin-right: 7px;
	max-width: 28px;
	max-height: 28px;
}
.c-w-r .p-b-w {
    margin: 30px auto 0 auto;
}
.c-w-p {
	max-width: 500px;
	margin: 0 auto;
	z-index: 2;
}
.prcs-l {
	text-align: center;
}
.prcs-l span {
	font-size: 7em;
	filter: invert(100%) sepia(0%) saturate(6500%) hue-rotate(262deg) brightness(112%) contrast(73%);
}
.prcs-m {
	text-align: center;
	min-height: 76px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	font-size: 1.6em;
	font-weight: 700;
}
.prcs-m span.material-icons-two-tone {
	color: #ccc;
	font-size: 1.6em;
}
.p-lb {
	width: 90%;
	overflow: hidden;
	position: relative;
	margin: 20px auto 0 auto;
	background: rgba(0, 0, 0, 0.1);
	border-radius: 10px;
}
.p-lb div {
	font-size: 12px;
	text-indent: 9999px;
	overflow: hidden;
	border-radius: 4px;
	position: relative;
}
.p-lb div:after {
	content: '';
	height: 100%;
	width: 100%;
	position: absolute;
	left: 0;
	top: 0;
	display: block;
	background: -moz-linear-gradient(top, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0.2) 50%, rgba(0,0,0,0) 51%, rgba(0,0,0,0) 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(top, rgba(0,0,0,0.2) 0%,rgba(0,0,0,0.2) 50%,rgba(0,0,0,0) 51%,rgba(0,0,0,0) 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to bottom, rgba(0,0,0,0.2) 0%,rgba(0,0,0,0.2) 50%,rgba(0,0,0,0) 51%,rgba(0,0,0,0) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#4d000000', endColorstr='#00000000',GradientType=0 ); /* IE6-9 */
}
.c-w-p-g-i-c {
	position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
}
.c-w-p-g-i {
	position: relative;
	box-shadow: 0 0 0 7px rgba(0, 0, 0, 0.3);
	border-radius: 7px;
	display: table;
	margin: 0 auto;
	opacity: 0;
}
.c-w-p-g-i-i {
	position: relative;
    background: #fff;
    border-radius: 7px;
    padding: 15px 30px 14px 30px;
	width: 250px;
	overflow: hidden;
	margin: 0 auto;
}
.c-w-p-g-i-i-c-w {
	width: 100px;
	height: 100px;
	transform: rotate(-45deg);
	position: absolute;
	right: -50px;
	top: -50px;
	text-align: center;
	box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.3);	
}
.c-w-p-g-i-i-c-w span {
	filter: invert(96%) sepia(0%) saturate(0%) hue-rotate(73deg) brightness(104%) contrast(104%);
	font-size: 1.4em;
	position: relative;
	top: 40px;
    right: 30px;
}
.c-w-p-g-i-i-c-w span.mitt-r {
	transform: rotate(45deg);
}
.c-w-p-g-i-i-t-i {
	max-width: 80px;
}
.c-w-p-g-i-i-b {
	text-align: center;
}
.c-w-p-g-i-i-b-v {
	font-size: 2em;
    font-weight: 900;
}
.d-b-notice-wrapper {
	text-align: center;
}
.d-b-notice-wrapper span.material-icons-two-tone {
	font-size: 7em;
	filter: invert(100%) sepia(0%) saturate(6500%) hue-rotate(262deg) brightness(112%) contrast(73%);
}
.d-b-notice-wrapper h2 {
	font-size: 2.2em;
	margin: 30px auto 10px auto;
}
.d-b-notice-wrapper p {
	margin: 0;
}
.c-w-p-g-i-i-t {
	text-align: center;
}
.c-w-p-g-i-i-b-v {
    background: #eee;
    border-radius: 7px;
    line-height: 1;
    padding: 5px 15px;
    margin: 7px auto 5px auto;
    display: table; 
}
.caSs-cm.caSs-cm-s {
	margin: 0 auto;
	height: 80px;
	width: 80px;
}
#cwPB {
    width: 100%;
    margin: 5px auto 0 auto;
}
#cwPB div {
    font-size: 7px;
}
.cwPBws #cwPB div {
	background: #83dd81;
}
.c-w-p-g-i-i-c-w. #cwPB div {
	-webkit-transition: background 0.2s ease-in-out 0s;
	-moz-transition: background 0.2s ease-in-out 0s;
	-ms-transition: background 0.2s ease-in-out 0s;
	-o-transition: background 0.2s ease-in-out 0s;
	transition: background 0.2s ease-in-out 0s;
}	
.c-w-p-g-i-i-c-w-s {
	background: #83dd81;
}
.acpeou {
	animation-duration: 0.8s;
	-webkit-animation-duration: 0.8s;
}
.prlp {
	position: fixed;
	width: 100%;
	height: 100%;
	left: 0; 
	top: 0;
	z-index: 200;
	background: #0050c9;
}
.prlw {
	position: fixed;
	width: 100%;
	height: 100%;
	left: 0; 
	top: 0;
	z-index: 200;
	background: #0050c9;
}
.prlw-r {
	position: absolute;
}
.prlw-l {
	position: absolute;
}
.prlc {
	position: fixed;
	width: 100%;
	height: 100%;
	left: 0; 
	top: 0;
	z-index: 200;
	display: flex;
	align-items: center;
	justify-content: center;
}
.prlc span.material-icons-two-tone {
	font-size: 10em;
	filter: invert(100%) sepia(0%) saturate(6500%) hue-rotate(262deg) brightness(112%) contrast(73%);
}
.prlc-i {
	width: 200px;
	margin: 0 auto;
	text-align: center;
}
.prlc-lb {
	height: 10px;
	width: 100%;
	margin: 10px auto 0 auto;
	border-radius: 50px;
	background: rgba(0, 0, 0, 0.2);
	position: relative;
	overflow: hidden;
}
.c-w-l-t-v {
	font-size: 1.8em;
	font-weight: 700;
	text-align: center;
}
.c-w-l-p-v {
	font-size: 1.2em;
	margin: 0;
	text-align: center;
}
.prlc-lb div {
	position: absolute;
	height: 100%;
	width: 0;
	left: 0;
	top: 0;
	background: #fff;
}
.c-w-l-r-o-w .c-w-p-g-i-i {
	box-shadow: 0 0 0 7px #83dd81;
}
.c-w-l-r-o-w .c-w-p-g-i-i-t-i {
    max-width: 60px;
}
.c-w-l-r-o-w #cwPB {
	max-width: 150px;
}
.c-w-l-r-o-w #cwPB div {
	background: #83dd81;
}
a#l-s-v-b {
	text-decoration: none !important;
	color: #fff;
}
a#l-s-v-b:hover {
	color: #fff;
}
.lsv2s {
	font-weight: 700;
}
/*--------------------------------------------------------------
# Responsive Design
--------------------------------------------------------------*/
@media screen and (max-width: 1920px) {
	.h-c .l-i {
		max-width: 350px;
		max-height: 50px;
	}
	header {
		min-height: 130px;
	}
	.m-s {
		min-height: calc(100vh - 130px);
	}
	.s-o-w {
		top: -38px;
	}
	.s-o {
		width: 70px;
		height: 70px;
	}
	.s-o span {
		font-size: 2.2em;
	}
	.c-w {
		padding: 50px 120px 40px 120px;
	}
	.c-w-r {
		padding: 50px 70px 40px 70px;
	}
}
@media screen and (max-width: 1440px) {
	.r-i-s-r-w-i {
		max-width: 50px;
		max-height: 50px;
	}
	.c-w-r {
		padding: 35px 70px 30px 70px;
	}
	.p-b {
		height: 70px;
	}
	.c-i-t {
		margin-bottom: 20px;
	}
	.c-w-r .p-b-w {
		margin: 20px auto 0 auto;
	}
	.prlc span.material-icons-two-tone {
		font-size: 7em;
	}
}
@media screen and (max-width: 1280px) {
	.h-c .l-i {
		max-width: 300px;
		max-height: 40px;
	}
	header {
		min-height: 115px;
	}
	.m-s {
		min-height: calc(100vh - 115px);
	}
	.r-s-i {
		padding: 8px 5px 6px 5px;
	}
	.r-i-s-r-m-v {
		font-size: 1.4em;
	}
}
@media screen and (max-width: 575px) {
	header {
		padding: 10px 0;
		min-height: 70px;
	}
	.h-c .l-i {
		max-width: 270px;
		max-height: 30px;
	}
	.m-s {
		min-height: calc(100vh - 70px);
		padding: 70px 15px;
	}
	.c-w {
		padding: 50px 30px 40px 30px;
	}
	.c-i-t span.c-i-t-v {
		font-size: 1.1em;
	}
	.u-i {
		height: 70px;
		padding-left: 58px;
		font-size: 1.2em;
	}
	.u-i-w span.material-icons-two-tone {
		left: 15px;
		top: 19px;
	}	
	.p-f-w {
		margin: 32px -5px 0 -5px;
	}
	.p-s-i {
		height: 70px;
	}
	.p-s-i i {
		font-size: 1.6em;
	}
	.s-e-w {
		bottom: -25px;
	}
	.s-e-w-i {
		font-size: 0.7em;
		padding: 1px 8px;
	}
	.p-b-w {
		margin: 40px auto 0 auto;
	}
	.s-f-p-w, .s-f-p-t-u-w  {
		width: 100%;
	}
	.s-f-p-t-u-w {
		height: 78px;
	}
	.s-f-p-t-u-v {
		font-size: 1.6em;
	}
	.s-f-p-t-p-w {
		width: 78px;
		height: 78px;
		font-size: 1.4em;
	}
	.s-f-p-t-l {
		font-size: 1.2em;
	}
	.s-f-p-a-w img {
		max-width: 125px;
	}
	.c-w-r {
		padding: 30px 30px 25px 30px;
	}
	.r-i-s-i-w {
		margin: 0 auto;
	}
	.r-s-i-i {
		max-width: 25px;
		max-height: 25px;
	}
	.r-i-s-r-m-v {
		font-size: 1.8em;
	}
	.r-i-s-r-m-l {
		font-size: 1.1em;
	}
	.prcs-l span {
		font-size: 5em;
	}
	.prcs-m {
		font-size: 1.2em;
	}
	.prlc-lb {
		height: 10px;
		width: 70px;
		left: -10%;
	}
	.c-w-p-g-i-i-t-i {
		max-width: 55px;
	}
	.caSs-cm.caSs-cm-s {
		height: 55px;
		width: 55px;
	}
	.c-w-l-p-v {
		font-size: 1em;
	}
	.c-w-p-g-i-i {
		padding: 10px 30px 10px 30px;
	}	
	.c-w-l-r-o-w .c-w-p-g-i-i-t-i {
		max-width: 50px;
	}
	.c-w-p-g-i-i-b-v {
		font-size: 1.6em;
	}
}
@media screen and (max-width: 320px) {
	header {
		padding: 5px 0;
		min-height: 80px;
	}
	.m-s {
		min-height: calc(100vh - 80px);
		padding: 15px 7px;
	}
	.c-w {
		padding: 30px 20px 30px 20px;
	}
	.c-i-t span.c-i-t-v {
		font-size: 1em;
	}
	.s-o-w {
		top: -23px;
	}
	.s-o {
		width: 45px;
		height: 45px;
		border: 3px solid #fff;
	}
	.s-o span {
		font-size: 1.5em;
	}
	.u-i-w span.material-icons-two-tone {
		left: 15px;
		top: 10px;
	}
	.u-i {
		height: 50px;
		padding-left: 58px;
		font-size: 1em;
	}
	.p-s-i {
		height: 50px;
	}
	.p-s-i i {
		font-size: 1.3em;
	}
	.p-b {
		height: 50px;
		font-size: 1.2em;
	}
	.prlc span.material-icons-two-tone {
		font-size: 5em;
	}
	.s-e-w {
		bottom: -18px;
	}
	.s-e-w-i {
		font-size: 0.55em;
	}
	.p-f-w {
		margin: 23px -5px 0 -5px;
	}
	.p-b-w {
		margin: 30px auto 0 auto;
	}
	.c-w-r {
		padding: 25px 20px 20px 20px;
	}
	.r-s-i {
		padding: 7px 5px 5px 5px;
		margin-bottom: 8px;
	}
	.r-s-i-i {
		max-width: 18px;
		max-height: 18px;
	}
	.r-s-i-v {
		font-size: 1em;
	}
	.r-s-i-l {
		top: -1px;
		font-size: 0.9em;
	}
	.r-i-s-r-w-i {
		max-width: 40px;
		max-height: 40px;
	}
	.r-i-s-r-w-i {
		margin-bottom: 4px;
	}
	.r-i-s-i-w {
		padding: 14px 10px 5px 10px;
	}	
	.r-s-i-w {
		margin-top: 15px;
	}
	.r-i-s-r-h-w {
		top: -9px;
	}
	.r-i-s-r-m-v {
		font-size: 1.5em;
	}
	.r-i-s-r-h-w span {
		padding: 4px 10px;
		font-size: 0.65em;
	}
	.c-w-r .p-b-w {
		margin: 14px auto 0 auto;
	}
	.prcs-l span {
		font-size: 4em;
	}
	.prcs-m {
		font-size: 1.2em;
		min-height: 56px;
	}
	.c-w-p-g-i-i-t-i {
		max-width: 45px;
	}
	.caSs-cm.caSs-cm-s {
		height: 45px;
		width: 45px;
	}
	.c-w-p-g-i-i-b-v {
		font-size: 1.6em;
	}
	.p-lb {
		margin: 10px auto 0 auto;
	}
}