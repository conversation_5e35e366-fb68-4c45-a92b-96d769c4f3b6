# PowerShell Script to Create Landing Pages for All Games
# This script copies the Brawl Stars landing page structure to all games and customizes it

Write-Host "Creating landing pages for all games..." -ForegroundColor Green

# Define games and their currencies
$games = @{
    "Fortnite" = @{ currency = "V-Bucks"; description = "Battle Royale" }
    "PUBG Mobile" = @{ currency = "UC"; description = "Battle Royale" }
    "Free Fire" = @{ currency = "Diamonds"; description = "Battle Royale" }
    "DLS 25" = @{ currency = "Coins"; description = "Football Simulation" }
    "eFootball" = @{ currency = "eFootball Coins"; description = "Football Simulation" }
    "FC MOBILE" = @{ currency = "FC Points"; description = "Football Simulation" }
    "Roblox" = @{ currency = "Robux"; description = "Gaming Platform" }
    "Monopoly GO" = @{ currency = "Dice Rolls"; description = "Board Game" }
    "Stumble Guys" = @{ currency = "Gems"; description = "Party Game" }
    "Royal Match" = @{ currency = "Coins"; description = "Puzzle Game" }
    "Lords Mobile" = @{ currency = "Gems"; description = "Strategy Game" }
    "Township" = @{ currency = "Coins"; description = "City Building" }
    "Bingo Blitz" = @{ currency = "Credits"; description = "Casino Game" }
    "Homescapes" = @{ currency = "Coins"; description = "Puzzle Game" }
    "Blood Strike" = @{ currency = "Diamonds"; description = "Battle Royale" }
    "BASEBALL 9" = @{ currency = "Coins"; description = "Sports Game" }
    "Match Master" = @{ currency = "Coins"; description = "Puzzle Game" }
    "Dragon City" = @{ currency = "Gems"; description = "Strategy Game" }
    "Candy Crush Saga" = @{ currency = "Gold Bars"; description = "Puzzle Game" }
    "Mech Arena" = @{ currency = "A-Coins"; description = "Action Game" }
    "Cooking Fever" = @{ currency = "Coins"; description = "Simulation Game" }
    "Board Kings" = @{ currency = "Rolls"; description = "Board Game" }
    "ZEPETO" = @{ currency = "Coins"; description = "Social Platform" }
    "DRAGON BALL LEGENDS" = @{ currency = "Chrono Crystals"; description = "Action RPG" }
    "One Piece Bounty Rush" = @{ currency = "Rainbow Diamonds"; description = "Action Game" }
    "Subway Surfers" = @{ currency = "Coins"; description = "Endless Runner" }
    "Clash of Clans" = @{ currency = "Gems"; description = "Strategy Game" }
    "8 Ball Pool" = @{ currency = "Coins"; description = "Sports Game" }
    "Pokemon Go" = @{ currency = "PokéCoins"; description = "AR Game" }
    "Car Parking" = @{ currency = "Money"; description = "Simulation Game" }
    "Car Parking Multiplayer 2" = @{ currency = "Money"; description = "Simulation Game" }
    "Chikii" = @{ currency = "Coins"; description = "Gaming Platform" }
    "TopTop" = @{ currency = "Coins"; description = "Social Platform" }
    "WePlay" = @{ currency = "Coins"; description = "Gaming Platform" }
    "Jawaker" = @{ currency = "Chips"; description = "Card Game" }
    "Hay Day" = @{ currency = "Coins"; description = "Farming Game" }
    "FROZEN CASH" = @{ currency = "Cash"; description = "Casino Game" }
    "Yalla Ludo" = @{ currency = "Diamonds"; description = "Board Game" }
    "Ludo Star" = @{ currency = "Coins"; description = "Board Game" }
    "Ludo Club" = @{ currency = "Coins"; description = "Board Game" }
    "Blockman Go" = @{ currency = "Cubes"; description = "Gaming Platform" }
    "CarX Street" = @{ currency = "Credits"; description = "Racing Game" }
    "Capybara Go" = @{ currency = "Coins"; description = "Adventure Game" }
    "PK XD" = @{ currency = "Coins"; description = "Social Game" }
    "Zooba" = @{ currency = "Gems"; description = "Battle Royale" }
    "Family Island" = @{ currency = "Rubies"; description = "Adventure Game" }
    "Coin Master" = @{ currency = "Spins"; description = "Casino Game" }
    "COD Mobile" = @{ currency = "CP"; description = "Battle Royale" }
    "Blooket" = @{ currency = "Tokens"; description = "Educational Game" }
    "Hero Wars" = @{ currency = "Emeralds"; description = "RPG Game" }
    "Hide Online" = @{ currency = "Coins"; description = "Action Game" }
    "HeadBall 2" = @{ currency = "Diamonds"; description = "Sports Game" }
    "World of Tanks Blitz" = @{ currency = "Gold"; description = "Action Game" }
    "Uno" = @{ currency = "Coins"; description = "Card Game" }
    "BitLife" = @{ currency = "Bitizenship"; description = "Life Simulation" }
    "Avakin Life" = @{ currency = "Avacoins"; description = "Social Game" }
    "Tango" = @{ currency = "Coins"; description = "Social Platform" }
    "HalaMe" = @{ currency = "Coins"; description = "Social Platform" }
    "Sugo" = @{ currency = "Coins"; description = "Social Platform" }
    "Bigo Live" = @{ currency = "Diamonds"; description = "Live Streaming" }
    "Poppo Live" = @{ currency = "Coins"; description = "Live Streaming" }
    "Tigo" = @{ currency = "Coins"; description = "Social Platform" }
    "Cafe" = @{ currency = "Coins"; description = "Social Platform" }
    "Starchat" = @{ currency = "Coins"; description = "Social Platform" }
    "Soulchill" = @{ currency = "Diamonds"; description = "Social Platform" }
    "Chamet" = @{ currency = "Diamonds"; description = "Social Platform" }
    "Azar" = @{ currency = "Gems"; description = "Social Platform" }
    "Top Follow" = @{ currency = "Coins"; description = "Social Platform" }
    "Timo" = @{ currency = "Coins"; description = "Social Platform" }
    "Golf Battle" = @{ currency = "Gems"; description = "Sports Game" }
    "Last War Survival" = @{ currency = "Diamonds"; description = "Strategy Game" }
    "SimCity BuildIt" = @{ currency = "SimCash"; description = "City Building" }
    "NBA Infinite" = @{ currency = "Coins"; description = "Sports Game" }
    "FarmVille 2" = @{ currency = "Farm Bucks"; description = "Farming Game" }
}

$sourceFolder = "Brawl Stars"
$processedCount = 0

foreach ($gameName in $games.Keys) {
    if ($gameName -eq "Brawl Stars") {
        continue # Skip the source folder
    }
    
    $gameInfo = $games[$gameName]
    $currency = $gameInfo.currency
    $description = $gameInfo.description
    
    Write-Host "Processing: $gameName" -ForegroundColor Yellow
    
    # Create assets folder structure if it doesn't exist
    $assetsPath = Join-Path $gameName "assets"
    $imgPath = Join-Path $assetsPath "img"
    $cssPath = Join-Path $assetsPath "css"
    $jsPath = Join-Path $assetsPath "js"
    $soundPath = Join-Path $assetsPath "sound"
    
    if (!(Test-Path $assetsPath)) { New-Item -ItemType Directory -Path $assetsPath -Force | Out-Null }
    if (!(Test-Path $imgPath)) { New-Item -ItemType Directory -Path $imgPath -Force | Out-Null }
    if (!(Test-Path $cssPath)) { New-Item -ItemType Directory -Path $cssPath -Force | Out-Null }
    if (!(Test-Path $jsPath)) { New-Item -ItemType Directory -Path $jsPath -Force | Out-Null }
    if (!(Test-Path $soundPath)) { New-Item -ItemType Directory -Path $soundPath -Force | Out-Null }
    
    # Copy CSS, JS, and Sound files
    try {
        Copy-Item "$sourceFolder\assets\css\*" $cssPath -Force -Recurse
        Copy-Item "$sourceFolder\assets\js\*" $jsPath -Force -Recurse
        Copy-Item "$sourceFolder\assets\sound\*" $soundPath -Force -Recurse
        
        # Copy and rename image files
        $sourceImgPath = "$sourceFolder\assets\img"
        
        # Copy favicon
        if (Test-Path "$sourceImgPath\favicon.ico") {
            Copy-Item "$sourceImgPath\favicon.ico" $imgPath -Force
        }
        
        # Copy mgi.png
        if (Test-Path "$sourceImgPath\mgi.png") {
            Copy-Item "$sourceImgPath\mgi.png" $imgPath -Force
        }
        
        # Rename existing images to new naming convention
        $gameIconOld = Join-Path $gameName "$gameName.jpg"
        $gameIconPng = Join-Path $gameName "$gameName.png"
        $gameIconNew = Join-Path $imgPath "gameicon.png"
        
        if (Test-Path $gameIconPng) {
            Copy-Item $gameIconPng $gameIconNew -Force
        } elseif (Test-Path $gameIconOld) {
            Copy-Item $gameIconOld $gameIconNew -Force
        }
        
        # Handle currency image
        $currencyOld = Join-Path $gameName "$currency.jpg"
        $currencyNew = Join-Path $imgPath "game currency.png"
        
        if (Test-Path $currencyOld) {
            Copy-Item $currencyOld $currencyNew -Force
        }
        
        # Handle wallpaper/background
        $wallpaperOld = Join-Path $gameName "wallpaper $gameName.jpg"
        $backgroundNew = Join-Path $imgPath "background.jpg"
        
        if (Test-Path $wallpaperOld) {
            Copy-Item $wallpaperOld $backgroundNew -Force
        }
        
        $processedCount++
        Write-Host "  ✅ Assets copied successfully" -ForegroundColor Green
        
    } catch {
        Write-Host "  ❌ Error copying assets: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nAssets copying completed!" -ForegroundColor Green
Write-Host "Processed $processedCount games" -ForegroundColor Cyan

# Now create customized HTML files
Write-Host "`nCreating customized HTML files..." -ForegroundColor Blue

foreach ($gameName in $games.Keys) {
    if ($gameName -eq "Brawl Stars") {
        continue # Skip the source folder
    }

    $gameInfo = $games[$gameName]
    $currency = $gameInfo.currency
    $description = $gameInfo.description

    # Read the source HTML file
    $sourceHtml = Get-Content "$sourceFolder\index.html" -Raw

    # Replace game-specific content
    $customHtml = $sourceHtml -replace "Brawl Stars", $gameName
    $customHtml = $customHtml -replace "Gems", $currency
    $customHtml = $customHtml -replace "gems and gold", "$currency and resources"
    $customHtml = $customHtml -replace "dominate Brawl Stars", "dominate $gameName"
    $customHtml = $customHtml -replace "Discover the Brawl Stars Generator", "Discover the $gameName Generator"
    $customHtml = $customHtml -replace "powerful Brawl Stars Generator", "powerful $gameName Generator"
    $customHtml = $customHtml -replace "WallpaperDog-20526055.jpg", "background.jpg"

    # Save the customized HTML file
    $htmlPath = Join-Path $gameName "index.html"
    $customHtml | Out-File -FilePath $htmlPath -Encoding UTF8

    # Also copy finalstep.html if it exists
    if (Test-Path "$sourceFolder\finalstep.html") {
        $finalStepHtml = Get-Content "$sourceFolder\finalstep.html" -Raw
        $customFinalStep = $finalStepHtml -replace "Brawl Stars", $gameName
        $customFinalStep = $customFinalStep -replace "Gems", $currency

        $finalStepPath = Join-Path $gameName "finalstep.html"
        $customFinalStep | Out-File -FilePath $finalStepPath -Encoding UTF8
    }

    Write-Host "  ✅ Created HTML files for $gameName" -ForegroundColor Green
}

Write-Host "`nAll landing pages created successfully!" -ForegroundColor Green
