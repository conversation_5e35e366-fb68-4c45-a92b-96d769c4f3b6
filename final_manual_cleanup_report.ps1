# Final Manual Cleanup Report
Write-Host "=== Final Manual Cleanup Report ===" -ForegroundColor Magenta

$gameFolders = Get-ChildItem -Directory | Where-Object { 
    $_.Name -notlike "*.ps1" -and 
    $_.Name -ne "*.html" -and
    $_.Name -ne "*.md" -and
    $_.Name -ne "Example Game"
}

$totalGames = $gameFolders.Count
$cleanGames = 0
$completeGames = 0

Write-Host "Checking $totalGames games..." -ForegroundColor Cyan

foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    
    # Check if main folder is clean
    $mainImages = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg") -and
        $_.Name -notlike "*.html"
    }
    
    if ($mainImages.Count -eq 0) {
        $cleanGames++
    }
    
    # Check if assets/img is complete
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    if (Test-Path $assetsImgPath) {
        $hasGameIcon = Test-Path (Join-Path $assetsImgPath "gameicon.png")
        $hasCurrency = Test-Path (Join-Path $assetsImgPath "game currency.png")
        $hasBackground = Test-Path (Join-Path $assetsImgPath "background.jpg")
        
        if ($hasGameIcon -and $hasCurrency -and $hasBackground) {
            $completeGames++
        }
        
        # Count files in assets/img
        $imgFiles = Get-ChildItem $assetsImgPath -File
        $status = if ($imgFiles.Count -eq 5) { "Perfect" } elseif ($imgFiles.Count -lt 5) { "Missing" } else { "Extra" }
        
        Write-Host "$gameName - Files: $($imgFiles.Count) - $status" -ForegroundColor $(if ($status -eq "Perfect") { "Green" } elseif ($status -eq "Missing") { "Red" } else { "Yellow" })
    }
}

Write-Host "`n=== Summary ===" -ForegroundColor Blue
Write-Host "Total games: $totalGames" -ForegroundColor Cyan
Write-Host "Games with clean main folders: $cleanGames" -ForegroundColor Green
Write-Host "Games with complete image sets: $completeGames" -ForegroundColor Green

$cleanRate = [math]::Round(($cleanGames / $totalGames) * 100, 1)
$completeRate = [math]::Round(($completeGames / $totalGames) * 100, 1)

Write-Host "Clean rate: $cleanRate%" -ForegroundColor Magenta
Write-Host "Complete rate: $completeRate%" -ForegroundColor Magenta

if ($cleanRate -eq 100 -and $completeRate -eq 100) {
    Write-Host "`nPERFECT! All games are clean and complete!" -ForegroundColor Green
} else {
    Write-Host "`nSome games may need additional attention" -ForegroundColor Yellow
}
