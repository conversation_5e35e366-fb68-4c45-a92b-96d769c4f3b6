<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بحث صور الألعاب - Games Image Search</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .search-section {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .search-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #4ecdc4;
        }
        
        .search-btn {
            padding: 15px 25px;
            background: #4ecdc4;
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .search-btn:hover {
            background: #45b7aa;
        }
        
        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            padding: 30px;
        }
        
        .game-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            border: 1px solid #e9ecef;
        }
        
        .game-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .game-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .search-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .search-button {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            text-decoration: none;
            text-align: center;
            display: block;
        }
        
        .icon-btn {
            background: #ff6b6b;
            color: white;
        }
        
        .icon-btn:hover {
            background: #ff5252;
            transform: scale(1.02);
        }
        
        .currency-btn {
            background: #ffd93d;
            color: #333;
        }
        
        .currency-btn:hover {
            background: #ffcd02;
            transform: scale(1.02);
        }
        
        .wallpaper-btn {
            background: #6c5ce7;
            color: white;
        }
        
        .wallpaper-btn:hover {
            background: #5f3dc4;
            transform: scale(1.02);
        }
        
        .filter-section {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .filter-btn {
            padding: 8px 16px;
            background: #e9ecef;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-btn.active {
            background: #4ecdc4;
            color: white;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 بحث صور الألعاب</h1>
            <p>ابحث عن أيقونات وصور الألعاب بسهولة</p>
        </div>
        
        <div class="search-section">
            <div class="search-box">
                <input type="text" class="search-input" id="searchInput" placeholder="ابحث عن لعبة...">
                <button class="search-btn" onclick="filterGames()">🔍 بحث</button>
            </div>
            
            <div class="filter-section">
                <span>تصفية:</span>
                <button class="filter-btn active" onclick="showAll()">الكل</button>
                <button class="filter-btn" onclick="filterByCategory('action')">أكشن</button>
                <button class="filter-btn" onclick="filterByCategory('sports')">رياضة</button>
                <button class="filter-btn" onclick="filterByCategory('puzzle')">ألغاز</button>
                <button class="filter-btn" onclick="filterByCategory('social')">اجتماعية</button>
            </div>
        </div>
        
        <div class="games-grid" id="gamesGrid">
            <!-- Games will be populated by JavaScript -->
        </div>
    </div>

    <script>
        const games = [
            { name: "Fortnite", currency: "V-Bucks", category: "action" },
            { name: "PUBG Mobile", currency: "UC", category: "action" },
            { name: "Free Fire", currency: "Diamonds", category: "action" },
            { name: "DLS 25", currency: "Coins", category: "sports" },
            { name: "eFootball", currency: "eFootball Coins", category: "sports" },
            { name: "FC MOBILE", currency: "FC Points", category: "sports" },
            { name: "Roblox", currency: "Robux", category: "social" },
            { name: "Monopoly GO", currency: "Dice Rolls", category: "puzzle" },
            { name: "Stumble Guys", currency: "Gems", category: "action" },
            { name: "Brawl Stars", currency: "Gems", category: "action" },
            { name: "Royal Match", currency: "Coins", category: "puzzle" },
            { name: "Lords Mobile", currency: "Gems", category: "action" },
            { name: "Township", currency: "Coins", category: "puzzle" },
            { name: "Bingo Blitz", currency: "Credits", category: "puzzle" },
            { name: "Homescapes", currency: "Coins", category: "puzzle" },
            { name: "Blood Strike", currency: "Diamonds", category: "action" },
            { name: "BASEBALL 9", currency: "Coins", category: "sports" },
            { name: "Match Master", currency: "Coins", category: "puzzle" },
            { name: "Dragon City", currency: "Gems", category: "puzzle" },
            { name: "Candy Crush Saga", currency: "Gold Bars", category: "puzzle" },
            { name: "Mech Arena", currency: "A-Coins", category: "action" },
            { name: "Cooking Fever", currency: "Coins", category: "puzzle" },
            { name: "Board Kings", currency: "Rolls", category: "puzzle" },
            { name: "ZEPETO", currency: "Coins", category: "social" },
            { name: "DRAGON BALL LEGENDS", currency: "Chrono Crystals", category: "action" },
            { name: "One Piece Bounty Rush", currency: "Rainbow Diamonds", category: "action" },
            { name: "Subway Surfers", currency: "Coins", category: "action" },
            { name: "Clash of Clans", currency: "Gems", category: "action" },
            { name: "8 Ball Pool", currency: "Coins", category: "sports" },
            { name: "Pokemon Go", currency: "PokéCoins", category: "action" },
            { name: "Car Parking", currency: "Money", category: "sports" },
            { name: "Car Parking Multiplayer 2", currency: "Money", category: "sports" },
            { name: "Chikii", currency: "Coins", category: "social" },
            { name: "TopTop", currency: "Coins", category: "social" },
            { name: "WePlay", currency: "Coins", category: "social" },
            { name: "Jawaker", currency: "Chips", category: "puzzle" },
            { name: "Hay Day", currency: "Coins", category: "puzzle" },
            { name: "FROZEN CASH", currency: "Cash", category: "puzzle" },
            { name: "Yalla Ludo", currency: "Diamonds", category: "puzzle" },
            { name: "Ludo Star", currency: "Coins", category: "puzzle" },
            { name: "Ludo Club", currency: "Coins", category: "puzzle" },
            { name: "Blockman Go", currency: "Cubes", category: "action" },
            { name: "CarX Street", currency: "Credits", category: "sports" },
            { name: "Capybara Go", currency: "Coins", category: "puzzle" },
            { name: "PK XD", currency: "Coins", category: "social" },
            { name: "Zooba", currency: "Gems", category: "action" },
            { name: "Family Island", currency: "Rubies", category: "puzzle" },
            { name: "Coin Master", currency: "Spins", category: "puzzle" },
            { name: "COD Mobile", currency: "CP", category: "action" },
            { name: "Blooket", currency: "Tokens", category: "puzzle" },
            { name: "Hero Wars", currency: "Emeralds", category: "action" },
            { name: "Hide Online", currency: "Coins", category: "action" },
            { name: "HeadBall 2", currency: "Diamonds", category: "sports" },
            { name: "World of Tanks Blitz", currency: "Gold", category: "action" },
            { name: "Uno", currency: "Coins", category: "puzzle" },
            { name: "BitLife", currency: "Bitizenship", category: "social" },
            { name: "Avakin Life", currency: "Avacoins", category: "social" },
            { name: "Tango", currency: "Coins", category: "social" },
            { name: "HalaMe", currency: "Coins", category: "social" },
            { name: "Sugo", currency: "Coins", category: "social" },
            { name: "Bigo Live", currency: "Diamonds", category: "social" },
            { name: "Poppo Live", currency: "Coins", category: "social" },
            { name: "Tigo", currency: "Coins", category: "social" },
            { name: "Cafe", currency: "Coins", category: "social" },
            { name: "Starchat", currency: "Coins", category: "social" },
            { name: "Soulchill", currency: "Diamonds", category: "social" },
            { name: "Chamet", currency: "Diamonds", category: "social" },
            { name: "Azar", currency: "Gems", category: "social" },
            { name: "Top Follow", currency: "Coins", category: "social" },
            { name: "Timo", currency: "Coins", category: "social" },
            { name: "Golf Battle", currency: "Gems", category: "sports" },
            { name: "Last War Survival", currency: "Diamonds", category: "action" },
            { name: "SimCity BuildIt", currency: "SimCash", category: "puzzle" },
            { name: "NBA Infinite", currency: "Coins", category: "sports" },
            { name: "FarmVille 2", currency: "Farm Bucks", category: "puzzle" }
        ];

        function createGameCard(game) {
            return `
                <div class="game-card" data-category="${game.category}">
                    <div class="game-title">${game.name}</div>
                    <div class="search-buttons">
                        <a href="https://www.google.com/search?tbm=isch&q=${encodeURIComponent(game.name + ' icon 512x512')}" 
                           target="_blank" class="search-button icon-btn">
                            🎮 بحث أيقونة اللعبة
                        </a>
                        <a href="https://www.google.com/search?tbm=isch&q=${encodeURIComponent(game.currency + ' ' + game.name + ' currency icon')}" 
                           target="_blank" class="search-button currency-btn">
                            💰 بحث عملة ${game.currency}
                        </a>
                        <a href="https://www.google.com/search?tbm=isch&q=${encodeURIComponent(game.name + ' wallpaper 4k')}" 
                           target="_blank" class="search-button wallpaper-btn">
                            🖼️ بحث خلفية اللعبة
                        </a>
                    </div>
                </div>
            `;
        }

        function renderGames(gamesToRender = games) {
            const gamesGrid = document.getElementById('gamesGrid');
            gamesGrid.innerHTML = gamesToRender.map(createGameCard).join('');
        }

        function filterGames() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const filteredGames = games.filter(game => 
                game.name.toLowerCase().includes(searchTerm) ||
                game.currency.toLowerCase().includes(searchTerm)
            );
            renderGames(filteredGames);
        }

        function filterByCategory(category) {
            // Update active filter button
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            const filteredGames = games.filter(game => game.category === category);
            renderGames(filteredGames);
        }

        function showAll() {
            // Update active filter button
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            renderGames(games);
        }

        // Search on Enter key
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                filterGames();
            }
        });

        // Initial render
        renderGames();
    </script>
</body>
</html>
