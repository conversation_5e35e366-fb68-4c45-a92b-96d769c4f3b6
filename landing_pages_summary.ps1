# PowerShell Script to Show Landing Pages Summary
# This script displays a comprehensive summary of all created landing pages

Write-Host "=== Landing Pages Summary ===" -ForegroundColor Magenta
Write-Host ""

# Get all game directories with landing pages
$gameFolders = Get-ChildItem -Directory | Where-Object { 
    $_.Name -notlike "*.ps1" -and 
    $_.Name -ne "*.html" -and
    (Test-Path (Join-Path $_.FullName "index.html"))
}

$totalGames = $gameFolders.Count
$completeGames = 0
$gamesWithAllImages = 0

Write-Host "Total games with landing pages: $totalGames" -ForegroundColor Green
Write-Host ""

Write-Host "Detailed Status:" -ForegroundColor Blue
Write-Host "===============" -ForegroundColor Blue

foreach ($folder in $gameFolders | Sort-Object Name) {
    $gameName = $folder.Name
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    
    # Check for required files
    $hasIndex = Test-Path (Join-Path $folder.FullName "index.html")
    $hasFinalStep = Test-Path (Join-Path $folder.FullName "finalstep.html")
    $hasGameIcon = Test-Path (Join-Path $assetsImgPath "gameicon.png")
    $hasCurrency = Test-Path (Join-Path $assetsImgPath "game currency.png")
    $hasBackground = Test-Path (Join-Path $assetsImgPath "background.jpg")
    $hasAssets = Test-Path (Join-Path $folder.FullName "assets")
    
    # Count complete games
    if ($hasIndex -and $hasFinalStep -and $hasAssets) {
        $completeGames++
    }
    
    if ($hasGameIcon -and $hasCurrency -and $hasBackground) {
        $gamesWithAllImages++
    }
    
    # Display status
    Write-Host "📁 $gameName" -ForegroundColor Yellow
    
    if ($hasIndex) {
        Write-Host "   ✅ index.html" -ForegroundColor Green
    } else {
        Write-Host "   ❌ index.html" -ForegroundColor Red
    }
    
    if ($hasFinalStep) {
        Write-Host "   ✅ finalstep.html" -ForegroundColor Green
    } else {
        Write-Host "   ❌ finalstep.html" -ForegroundColor Red
    }
    
    if ($hasAssets) {
        Write-Host "   ✅ assets folder" -ForegroundColor Green
    } else {
        Write-Host "   ❌ assets folder" -ForegroundColor Red
    }
    
    # Image status
    Write-Host "   Images:" -ForegroundColor Cyan
    if ($hasGameIcon) {
        Write-Host "     ✅ gameicon.png" -ForegroundColor Green
    } else {
        Write-Host "     ❌ gameicon.png" -ForegroundColor Red
    }
    
    if ($hasCurrency) {
        Write-Host "     ✅ game currency.png" -ForegroundColor Green
    } else {
        Write-Host "     ❌ game currency.png" -ForegroundColor Red
    }
    
    if ($hasBackground) {
        Write-Host "     ✅ background.jpg" -ForegroundColor Green
    } else {
        Write-Host "     ❌ background.jpg" -ForegroundColor Red
    }
    
    Write-Host ""
}

# Final statistics
Write-Host "=== Final Statistics ===" -ForegroundColor Magenta
Write-Host "Total games: $totalGames" -ForegroundColor Cyan
Write-Host "Games with complete landing pages: $completeGames" -ForegroundColor Green
Write-Host "Games with all images: $gamesWithAllImages" -ForegroundColor Green
Write-Host "Landing pages completion rate: $([math]::Round(($completeGames / $totalGames) * 100, 1))%" -ForegroundColor Yellow
Write-Host "Images completion rate: $([math]::Round(($gamesWithAllImages / $totalGames) * 100, 1))%" -ForegroundColor Yellow

Write-Host ""
Write-Host "=== File Structure Created ===" -ForegroundColor Blue
Write-Host "Each game now has:" -ForegroundColor White
Write-Host "  📄 index.html (customized landing page)" -ForegroundColor Cyan
Write-Host "  📄 finalstep.html (final step page)" -ForegroundColor Cyan
Write-Host "  📁 assets/" -ForegroundColor Cyan
Write-Host "    📁 css/ (stylesheets)" -ForegroundColor Gray
Write-Host "    📁 js/ (JavaScript files)" -ForegroundColor Gray
Write-Host "    📁 sound/ (sound effects)" -ForegroundColor Gray
Write-Host "    📁 img/" -ForegroundColor Gray
Write-Host "      🖼️  gameicon.png (game logo)" -ForegroundColor Green
Write-Host "      💰 game currency.png (currency image)" -ForegroundColor Green
Write-Host "      🖼️  background.jpg (wallpaper)" -ForegroundColor Green
Write-Host "      🔧 favicon.ico, mgi.png (additional assets)" -ForegroundColor Gray

Write-Host ""
Write-Host "🎉 Landing pages setup completed successfully!" -ForegroundColor Green
