# PowerShell Script to Organize Game Icons
# This script moves PNG files to their corresponding game folders and renames them

Write-Host "Organizing game icons..." -ForegroundColor Green

# Define mapping of PNG files to game folders
$iconMappings = @{
    "Bingo Blitz.png" = "Bingo Blitz"
    "Monopoly GO.png" = "Monopoly GO"
    "Roblox.png" = "Roblox"
    "Stumble Guys.png" = "Stumble Guys"
    "Township.png" = "Township"
    "FC MOBILE.jpg" = "FC MOBILE"
    "ff.png" = "Free Fire"
    "p.png" = "PUBG Mobile"
    "f.png" = "Fortnite"
    "d.png" = "DLS 25"
    "e.png" = "eFootball"
    "g.png" = "Golf Battle"
}

# Additional files that might need identification
$unknownFiles = @(
    "IconImage_20250509052312608_NEW_WAP_ICON_512_512.png",
    "IconImage_20250623084816771_NEW_WAP_ICON_512_512.png"
)

foreach ($file in $iconMappings.Keys) {
    $gameName = $iconMappings[$file]
    $gamePath = $gameName
    
    if (Test-Path $file) {
        if (Test-Path $gamePath) {
            # Determine the new filename based on the game name
            $newFileName = "$gameName.png"
            $destinationPath = Join-Path $gamePath $newFileName
            
            # Move and rename the file
            Move-Item -Path $file -Destination $destinationPath -Force
            Write-Host "✅ Moved $file to $gamePath\$newFileName" -ForegroundColor Green
            
            # Remove the old .jpg placeholder if it exists
            $oldPlaceholder = Join-Path $gamePath "$gameName.jpg"
            if (Test-Path $oldPlaceholder) {
                Remove-Item $oldPlaceholder -Force
                Write-Host "   🗑️  Removed old placeholder: $gameName.jpg" -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ Game folder not found: $gamePath" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  File not found: $file" -ForegroundColor Yellow
    }
}

# Handle unknown files
Write-Host "`nUnknown files found:" -ForegroundColor Cyan
foreach ($file in $unknownFiles) {
    if (Test-Path $file) {
        Write-Host "❓ $file - Please identify manually" -ForegroundColor Magenta
    }
}

Write-Host "`nIcon organization completed!" -ForegroundColor Green
