# PowerShell Script to Complete Image Setup
# This script ensures all games have the correct images in assets/img folder

Write-Host "Completing image setup for all games..." -ForegroundColor Green

# Get all game directories that have assets folder
$gameFolders = Get-ChildItem -Directory | Where-Object { 
    $_.Name -notlike "*.ps1" -and 
    $_.Name -ne "*.html" -and
    (Test-Path (Join-Path $_.FullName "assets"))
}

foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    Write-Host "Processing: $gameName" -ForegroundColor Yellow
    
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    
    # Ensure assets/img folder exists
    if (!(Test-Path $assetsImgPath)) {
        New-Item -ItemType Directory -Path $assetsImgPath -Force | Out-Null
    }
    
    # Copy game icon if missing
    $gameIconPath = Join-Path $assetsImgPath "gameicon.png"
    if (!(Test-Path $gameIconPath)) {
        # Look for game icon in main folder
        $mainGameIconPng = Join-Path $folder.FullName "$gameName.png"
        $mainGameIconJpg = Join-Path $folder.FullName "$gameName.jpg"
        
        if (Test-Path $mainGameIconPng) {
            Copy-Item $mainGameIconPng $gameIconPath -Force
            Write-Host "  ✅ Copied game icon (PNG)" -ForegroundColor Green
        } elseif (Test-Path $mainGameIconJpg) {
            Copy-Item $mainGameIconJpg $gameIconPath -Force
            Write-Host "  ✅ Copied game icon (JPG)" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️  No game icon found" -ForegroundColor Yellow
        }
    }
    
    # Copy currency image if missing
    $currencyPath = Join-Path $assetsImgPath "game currency.png"
    if (!(Test-Path $currencyPath)) {
        # Find currency file in main folder
        $currencyFiles = Get-ChildItem $folder.FullName -File | Where-Object { 
            $_.Name -notlike "wallpaper*" -and 
            $_.Name -ne "$gameName.jpg" -and 
            $_.Name -ne "$gameName.png" -and
            $_.Name -notlike "*.html" -and
            ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png")
        }
        
        if ($currencyFiles.Count -gt 0) {
            $currencyFile = $currencyFiles[0]
            Copy-Item $currencyFile.FullName $currencyPath -Force
            Write-Host "  ✅ Copied currency image: $($currencyFile.Name)" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️  No currency image found" -ForegroundColor Yellow
        }
    }
    
    # Copy background image if missing
    $backgroundPath = Join-Path $assetsImgPath "background.jpg"
    if (!(Test-Path $backgroundPath)) {
        # Look for wallpaper in main folder
        $wallpaperFile = Join-Path $folder.FullName "wallpaper $gameName.jpg"
        
        if (Test-Path $wallpaperFile) {
            Copy-Item $wallpaperFile $backgroundPath -Force
            Write-Host "  ✅ Copied background image" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️  No background image found" -ForegroundColor Yellow
        }
    }
}

Write-Host "`nImage setup completed!" -ForegroundColor Green

# Show final summary
Write-Host "`nFinal Summary:" -ForegroundColor Blue
$totalGames = 0
$completeGames = 0

foreach ($folder in $gameFolders) {
    $totalGames++
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    
    $hasGameIcon = Test-Path (Join-Path $assetsImgPath "gameicon.png")
    $hasCurrency = Test-Path (Join-Path $assetsImgPath "game currency.png")
    $hasBackground = Test-Path (Join-Path $assetsImgPath "background.jpg")
    
    if ($hasGameIcon -and $hasCurrency -and $hasBackground) {
        $completeGames++
        Write-Host "  ✅ $($folder.Name) - Complete" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️  $($folder.Name) - Missing:" -ForegroundColor Yellow
        if (!$hasGameIcon) { Write-Host "    - gameicon.png" -ForegroundColor Red }
        if (!$hasCurrency) { Write-Host "    - game currency.png" -ForegroundColor Red }
        if (!$hasBackground) { Write-Host "    - background.jpg" -ForegroundColor Red }
    }
}

Write-Host "`nTotal games with landing pages: $totalGames" -ForegroundColor Cyan
Write-Host "Games with complete image sets: $completeGames" -ForegroundColor Green
Write-Host "Completion rate: $([math]::Round(($completeGames / $totalGames) * 100, 1))%" -ForegroundColor Magenta
