# PowerShell Script to Clean Up Extra Images
# This script removes images outside assets/img folders after ensuring they're properly organized

Write-Host "Cleaning up extra images outside assets/img folders..." -ForegroundColor Green

# Get all game directories
$gameFolders = Get-ChildItem -Directory | Where-Object { 
    $_.Name -notlike "*.ps1" -and 
    $_.Name -ne "*.html" -and
    $_.Name -ne "*.md" -and
    (Test-Path (Join-Path $_.FullName "assets"))
}

$totalCleaned = 0

foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    
    # Check if the game has complete image set in assets/img
    $gameIconPath = Join-Path $assetsImgPath "gameicon.png"
    $currencyPath = Join-Path $assetsImgPath "game currency.png"
    $backgroundPath = Join-Path $assetsImgPath "background.jpg"
    
    $hasCompleteSet = (Test-Path $gameIconPath) -and (Test-Path $currencyPath) -and (Test-Path $backgroundPath)
    
    if ($hasCompleteSet) {
        # Get all image files in the main folder
        $extraImages = Get-ChildItem $folder.FullName -File | Where-Object { 
            ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg") -and
            $_.Name -notlike "*.html"
        }
        
        if ($extraImages.Count -gt 0) {
            Write-Host "`n📁 $gameName - Cleaning $($extraImages.Count) extra images" -ForegroundColor Yellow
            
            foreach ($extraImage in $extraImages) {
                try {
                    Remove-Item $extraImage.FullName -Force
                    Write-Host "  🗑️  Removed: $($extraImage.Name)" -ForegroundColor Gray
                    $totalCleaned++
                } catch {
                    Write-Host "  ❌ Could not remove: $($extraImage.Name)" -ForegroundColor Red
                }
            }
        }
    } else {
        Write-Host "`n⚠️  $gameName - Incomplete image set, skipping cleanup" -ForegroundColor Yellow
    }
}

# Clean up root level images
Write-Host "`nCleaning up root level images..." -ForegroundColor Blue
$rootImages = Get-ChildItem . -File | Where-Object { 
    ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg") -and
    $_.Name -notlike "*.html" -and
    $_.Name -notlike "IconImage*"  # Keep these for manual identification
}

foreach ($rootImage in $rootImages) {
    try {
        Remove-Item $rootImage.FullName -Force
        Write-Host "  🗑️  Removed root image: $($rootImage.Name)" -ForegroundColor Gray
        $totalCleaned++
    } catch {
        Write-Host "  ❌ Could not remove: $($rootImage.Name)" -ForegroundColor Red
    }
}

Write-Host "`n✅ Cleanup completed!" -ForegroundColor Green
Write-Host "Total images cleaned: $totalCleaned" -ForegroundColor Cyan

# Final verification
Write-Host "`nFinal verification..." -ForegroundColor Blue
$completeGames = 0
$totalGames = $gameFolders.Count

foreach ($folder in $gameFolders) {
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    $gameIconPath = Join-Path $assetsImgPath "gameicon.png"
    $currencyPath = Join-Path $assetsImgPath "game currency.png"
    $backgroundPath = Join-Path $assetsImgPath "background.jpg"
    
    $hasCompleteSet = (Test-Path $gameIconPath) -and (Test-Path $currencyPath) -and (Test-Path $backgroundPath)
    if ($hasCompleteSet) { $completeGames++ }
}

$completionRate = [math]::Round(($completeGames / $totalGames) * 100, 1)
Write-Host "Games with complete image sets: $completeGames/$totalGames ($completionRate%)" -ForegroundColor Green

if ($completionRate -eq 100) {
    Write-Host "🎉 Perfect! All games are properly organized!" -ForegroundColor Green
} else {
    Write-Host "⚠️ Some games still need attention." -ForegroundColor Yellow
}
