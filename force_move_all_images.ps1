# PowerShell Script to FORCE Move ALL Images to assets/img
# This script aggressively moves every single image file to assets/img

Write-Host "FORCE moving ALL images to assets/img folders..." -ForegroundColor Red

# Get all game directories
$gameFolders = Get-ChildItem -Directory | Where-Object { 
    $_.Name -notlike "*.ps1" -and 
    $_.Name -ne "*.html" -and
    $_.Name -ne "*.md" -and
    $_.Name -ne "Example Game"
}

$totalMoved = 0

foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    Write-Host "`n📁 Processing: $gameName" -ForegroundColor Yellow
    
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    
    # Ensure assets/img folder exists
    if (!(Test-Path $assetsImgPath)) {
        New-Item -ItemType Directory -Path $assetsImgPath -Force | Out-Null
        Write-Host "  📂 Created assets/img folder" -ForegroundColor Cyan
    }
    
    # Get ALL files that could be images (very broad search)
    $allFiles = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or 
         $_.Extension -eq ".png" -or 
         $_.Extension -eq ".jpeg" -or 
         $_.Extension -eq ".webp" -or 
         $_.Extension -eq ".bmp" -or 
         $_.Extension -eq ".gif" -or
         $_.Extension -eq ".svg" -or
         $_.Extension -eq ".tiff" -or
         $_.Extension -eq ".ico" -or
         $_.Extension -eq "" -and (
             $_.Name -like "*gameicon*" -or 
             $_.Name -like "*background*" -or 
             $_.Name -like "*currency*" -or
             $_.Name -like "*icon*" -or
             $_.Name -like "*image*" -or
             $_.Name -like "*img*" -or
             $_.Name -like "*wallpaper*" -or
             $_.Name -like "*logo*" -or
             $_.Name -like "*coin*" -or
             $_.Name -like "*gem*" -or
             $_.Name -like "*diamond*"
         )) -and
        $_.Name -notlike "*.html" -and
        $_.Name -notlike "*.css" -and
        $_.Name -notlike "*.js"
    }
    
    if ($allFiles.Count -eq 0) {
        Write-Host "  ✅ No images found in main folder" -ForegroundColor Green
        continue
    }
    
    Write-Host "  📷 Found $($allFiles.Count) files to move" -ForegroundColor Cyan
    
    foreach ($file in $allFiles) {
        $fileName = $file.Name
        $filePath = $file.FullName
        
        Write-Host "    📸 Moving: $fileName" -ForegroundColor Gray
        
        # Determine target name
        $targetName = $fileName
        if ($file.Extension -eq "") {
            $targetName = "$fileName.png"  # Add extension if missing
        }
        
        $targetPath = Join-Path $assetsImgPath $targetName
        
        # Handle name conflicts
        if (Test-Path $targetPath) {
            $counter = 1
            $baseName = [System.IO.Path]::GetFileNameWithoutExtension($targetName)
            $extension = [System.IO.Path]::GetExtension($targetName)
            if ($extension -eq "") { $extension = ".png" }
            
            do {
                $newTargetName = "$baseName($counter)$extension"
                $targetPath = Join-Path $assetsImgPath $newTargetName
                $counter++
            } while (Test-Path $targetPath)
            
            $targetName = $newTargetName
            Write-Host "      📝 Renamed to: $targetName" -ForegroundColor Magenta
        }
        
        # Move the file
        try {
            Move-Item $filePath $targetPath -Force
            $totalMoved++
            Write-Host "      ✅ Moved to assets/img/$targetName" -ForegroundColor Green
        } catch {
            Write-Host "      ❌ Error: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # Verify no images remain
    $remainingImages = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg" -or $_.Extension -eq ".webp" -or $_.Extension -eq ".bmp" -or $_.Extension -eq ".gif") -and
        $_.Name -notlike "*.html"
    }
    
    if ($remainingImages.Count -eq 0) {
        Write-Host "  🎉 All images moved successfully!" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️  $($remainingImages.Count) images still remain:" -ForegroundColor Yellow
        foreach ($remaining in $remainingImages) {
            Write-Host "    - $($remaining.Name)" -ForegroundColor Red
        }
    }
    
    # Show what's now in assets/img
    $imagesInAssetsImg = Get-ChildItem $assetsImgPath -File | Where-Object { 
        $_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg" -or $_.Extension -eq ".webp" -or $_.Extension -eq ".bmp" -or $_.Extension -eq ".gif"
    }
    Write-Host "  📁 Images now in assets/img: $($imagesInAssetsImg.Count)" -ForegroundColor Cyan
}

Write-Host "`n=== Final Summary ===" -ForegroundColor Magenta
Write-Host "Total images moved: $totalMoved" -ForegroundColor Green

# Final verification
Write-Host "`nFinal verification..." -ForegroundColor Blue
$gamesWithCleanFolders = 0
$totalGames = $gameFolders.Count

foreach ($folder in $gameFolders) {
    $remainingImages = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg" -or $_.Extension -eq ".webp" -or $_.Extension -eq ".bmp" -or $_.Extension -eq ".gif") -and
        $_.Name -notlike "*.html"
    }
    
    if ($remainingImages.Count -eq 0) {
        $gamesWithCleanFolders++
    } else {
        Write-Host "⚠️  $($folder.Name) still has $($remainingImages.Count) images outside assets/img" -ForegroundColor Yellow
    }
}

$cleanRate = [math]::Round(($gamesWithCleanFolders / $totalGames) * 100, 1)
Write-Host "`nGames with all images in assets/img: $gamesWithCleanFolders/$totalGames ($cleanRate%)" -ForegroundColor Green

if ($cleanRate -eq 100) {
    Write-Host "🎉 SUCCESS! All images are now in assets/img folders!" -ForegroundColor Green
} else {
    Write-Host "⚠️  Some images may still need manual attention" -ForegroundColor Yellow
}

Write-Host "`n📋 Mission: Move ALL images to assets/img - COMPLETED!" -ForegroundColor Blue
