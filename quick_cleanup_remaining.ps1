# Quick cleanup for remaining games
Write-Host "Quick cleanup of remaining games..." -ForegroundColor Green

$gameFolders = Get-ChildItem -Directory | Where-Object { 
    $_.Name -notlike "*.ps1" -and 
    $_.Name -ne "*.html" -and
    $_.Name -ne "*.md" -and
    $_.Name -ne "Example Game"
}

foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    Write-Host "Processing: $gameName" -ForegroundColor Yellow
    
    # Remove images from main folder
    $mainImages = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg") -and
        $_.Name -notlike "*.html"
    }
    
    foreach ($image in $mainImages) {
        try {
            Remove-Item $image.FullName -Force
            Write-Host "  Removed: $($image.Name)" -ForegroundColor Gray
        } catch {
            Write-Host "  Error removing: $($image.Name)" -ForegroundColor Red
        }
    }
    
    # Check assets/img folder
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    if (Test-Path $assetsImgPath) {
        $imgFiles = Get-ChildItem $assetsImgPath -File
        $requiredFiles = @("gameicon.png", "game currency.png", "background.jpg", "favicon.ico", "mgi.png")
        
        # Remove extra files
        foreach ($file in $imgFiles) {
            if ($file.Name -notin $requiredFiles) {
                try {
                    Remove-Item $file.FullName -Force
                    Write-Host "  Removed from img: $($file.Name)" -ForegroundColor Gray
                } catch {
                    Write-Host "  Error removing from img: $($file.Name)" -ForegroundColor Red
                }
            }
        }
        
        # Add missing files
        if (!(Test-Path (Join-Path $assetsImgPath "gameicon.png"))) {
            "Placeholder" | Out-File (Join-Path $assetsImgPath "gameicon.png")
        }
        if (!(Test-Path (Join-Path $assetsImgPath "game currency.png"))) {
            "Placeholder" | Out-File (Join-Path $assetsImgPath "game currency.png")
        }
        if (!(Test-Path (Join-Path $assetsImgPath "background.jpg"))) {
            "Placeholder" | Out-File (Join-Path $assetsImgPath "background.jpg")
        }
    }
}

Write-Host "Quick cleanup completed!" -ForegroundColor Green
