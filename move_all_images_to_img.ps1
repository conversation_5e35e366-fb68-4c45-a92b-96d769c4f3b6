# PowerShell Script to Move ALL Images to assets/img Folder
# This script moves every single image from game folders to assets/img

Write-Host "Moving ALL images to assets/img folders..." -ForegroundColor Green

# Get all game directories
$gameFolders = Get-ChildItem -Directory | Where-Object { 
    $_.Name -notlike "*.ps1" -and 
    $_.Name -ne "*.html" -and
    $_.Name -ne "*.md" -and
    $_.Name -ne "Example Game"  # Skip example folder
}

$totalProcessed = 0
$totalMoved = 0

foreach ($folder in $gameFolders) {
    $gameName = $folder.Name
    Write-Host "`n📁 Processing: $gameName" -ForegroundColor Yellow
    
    $assetsImgPath = Join-Path $folder.FullName "assets\img"
    
    # Ensure assets/img folder exists
    if (!(Test-Path $assetsImgPath)) {
        New-Item -ItemType Directory -Path $assetsImgPath -Force | Out-Null
        Write-Host "  📂 Created assets/img folder" -ForegroundColor Cyan
    }
    
    # Get ALL image files in the game folder (excluding assets subfolder)
    $allImageFiles = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or 
         $_.Extension -eq ".png" -or 
         $_.Extension -eq ".jpeg" -or 
         $_.Extension -eq ".webp" -or 
         $_.Extension -eq ".bmp" -or 
         $_.Extension -eq ".gif" -or
         $_.Extension -eq ".svg" -or
         $_.Extension -eq ".tiff" -or
         $_.Extension -eq ".ico") -and
        $_.Name -notlike "*.html"
    }
    
    # Also check for files without extensions that might be images
    $filesWithoutExt = Get-ChildItem $folder.FullName -File | Where-Object { 
        $_.Extension -eq "" -and
        ($_.Name -like "*gameicon*" -or 
         $_.Name -like "*background*" -or 
         $_.Name -like "*currency*" -or
         $_.Name -like "*icon*" -or
         $_.Name -like "*image*" -or
         $_.Name -like "*img*" -or
         $_.Name -like "*wallpaper*")
    }
    
    # Combine all image files
    $allImages = $allImageFiles + $filesWithoutExt
    
    if ($allImages.Count -eq 0) {
        Write-Host "  ℹ️  No images found in main folder" -ForegroundColor Gray
        $totalProcessed++
        continue
    }
    
    Write-Host "  📷 Found $($allImages.Count) images to move" -ForegroundColor Cyan
    
    # Track what we have and what we need
    $gameIconSet = Test-Path (Join-Path $assetsImgPath "gameicon.png")
    $currencySet = Test-Path (Join-Path $assetsImgPath "game currency.png")
    $backgroundSet = Test-Path (Join-Path $assetsImgPath "background.jpg")
    
    foreach ($imageFile in $allImages) {
        $fileName = $imageFile.Name
        $fileBaseName = $imageFile.BaseName
        $filePath = $imageFile.FullName
        $fileExtension = $imageFile.Extension
        
        Write-Host "    📸 Processing: $fileName" -ForegroundColor Gray
        
        $targetPath = ""
        $targetName = ""
        $moved = $false
        
        # Priority 1: Exact name matches for standard files
        if (($fileBaseName -eq "gameicon" -or $fileName -eq "gameicon") -and !$gameIconSet) {
            $targetPath = Join-Path $assetsImgPath "gameicon.png"
            $targetName = "gameicon.png"
            $gameIconSet = $true
            Write-Host "      ➡️  → gameicon.png (exact match)" -ForegroundColor Green
        }
        elseif (($fileBaseName -eq "game currency" -or $fileName -eq "game currency") -and !$currencySet) {
            $targetPath = Join-Path $assetsImgPath "game currency.png"
            $targetName = "game currency.png"
            $currencySet = $true
            Write-Host "      ➡️  → game currency.png (exact match)" -ForegroundColor Green
        }
        elseif (($fileBaseName -eq "background" -or $fileName -eq "background") -and !$backgroundSet) {
            $targetPath = Join-Path $assetsImgPath "background.jpg"
            $targetName = "background.jpg"
            $backgroundSet = $true
            Write-Host "      ➡️  → background.jpg (exact match)" -ForegroundColor Green
        }
        
        # Priority 2: Pattern matching for standard files
        elseif (($fileName -like "*$gameName*" -or $fileName -like "*gameicon*" -or $fileName -like "*icon*" -or $fileName -like "*logo*") -and !$gameIconSet) {
            $targetPath = Join-Path $assetsImgPath "gameicon.png"
            $targetName = "gameicon.png"
            $gameIconSet = $true
            Write-Host "      ➡️  → gameicon.png (pattern match)" -ForegroundColor Yellow
        }
        elseif (($fileName -like "*currency*" -or $fileName -like "*coin*" -or $fileName -like "*gem*" -or $fileName -like "*diamond*" -or $fileName -like "*money*" -or $fileName -like "*token*" -or $fileName -like "*credit*" -or $fileName -like "*cash*" -or $fileName -like "*point*" -or $fileName -like "*buck*" -or $fileName -like "*robux*" -or $fileName -like "*uc*" -or $fileName -like "*cp*") -and !$currencySet) {
            $targetPath = Join-Path $assetsImgPath "game currency.png"
            $targetName = "game currency.png"
            $currencySet = $true
            Write-Host "      ➡️  → game currency.png (pattern match)" -ForegroundColor Yellow
        }
        elseif (($fileName -like "*wallpaper*" -or $fileName -like "*background*" -or $fileName -like "*wall*" -or $fileName -like "*bg*") -and !$backgroundSet) {
            $targetPath = Join-Path $assetsImgPath "background.jpg"
            $targetName = "background.jpg"
            $backgroundSet = $true
            Write-Host "      ➡️  → background.jpg (pattern match)" -ForegroundColor Yellow
        }
        
        # Priority 3: Move any remaining images with original names
        else {
            # Keep original name but ensure it has an extension
            if ($fileExtension -eq "") {
                $targetName = "$fileName.png"  # Add .png if no extension
            } else {
                $targetName = $fileName
            }
            $targetPath = Join-Path $assetsImgPath $targetName
            Write-Host "      ➡️  → $targetName (keeping original name)" -ForegroundColor Cyan
        }
        
        # Move the file
        if ($targetPath -ne "") {
            try {
                # Check if target already exists
                if (Test-Path $targetPath) {
                    $counter = 1
                    $baseName = [System.IO.Path]::GetFileNameWithoutExtension($targetName)
                    $extension = [System.IO.Path]::GetExtension($targetName)
                    do {
                        $newTargetName = "$baseName($counter)$extension"
                        $targetPath = Join-Path $assetsImgPath $newTargetName
                        $counter++
                    } while (Test-Path $targetPath)
                    Write-Host "      📝 Renamed to avoid conflict: $newTargetName" -ForegroundColor Magenta
                }
                
                Copy-Item $filePath $targetPath -Force
                Remove-Item $filePath -Force
                $totalMoved++
                $moved = $true
                Write-Host "      ✅ Moved successfully" -ForegroundColor Green
            } catch {
                Write-Host "      ❌ Error moving file: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
    
    $totalProcessed++
    
    # Show final status for this game
    $finalGameIcon = Test-Path (Join-Path $assetsImgPath "gameicon.png")
    $finalCurrency = Test-Path (Join-Path $assetsImgPath "game currency.png")
    $finalBackground = Test-Path (Join-Path $assetsImgPath "background.jpg")
    $totalImagesInFolder = (Get-ChildItem $assetsImgPath -File | Where-Object { $_.Extension -ne ".ico" -and $_.Name -ne "mgi.png" }).Count
    
    Write-Host "  📊 Final Status:" -ForegroundColor Cyan
    if ($finalGameIcon) { Write-Host "    ✅ gameicon.png" -ForegroundColor Green } else { Write-Host "    ❌ gameicon.png" -ForegroundColor Red }
    if ($finalCurrency) { Write-Host "    ✅ game currency.png" -ForegroundColor Green } else { Write-Host "    ❌ game currency.png" -ForegroundColor Red }
    if ($finalBackground) { Write-Host "    ✅ background.jpg" -ForegroundColor Green } else { Write-Host "    ❌ background.jpg" -ForegroundColor Red }
    Write-Host "    📁 Total images in assets/img: $totalImagesInFolder" -ForegroundColor White
    
    # Check if any images remain in main folder
    $remainingImages = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg" -or $_.Extension -eq ".webp" -or $_.Extension -eq ".bmp" -or $_.Extension -eq ".gif") -and
        $_.Name -notlike "*.html"
    }
    
    if ($remainingImages.Count -eq 0) {
        Write-Host "    🎉 All images moved to assets/img!" -ForegroundColor Green
    } else {
        Write-Host "    ⚠️  $($remainingImages.Count) images still in main folder" -ForegroundColor Yellow
    }
}

Write-Host "`n=== Final Summary ===" -ForegroundColor Magenta
Write-Host "Games processed: $totalProcessed" -ForegroundColor Cyan
Write-Host "Total images moved: $totalMoved" -ForegroundColor Green

# Count games with no images outside assets/img
$cleanGames = 0
foreach ($folder in $gameFolders) {
    $remainingImages = Get-ChildItem $folder.FullName -File | Where-Object { 
        ($_.Extension -eq ".jpg" -or $_.Extension -eq ".png" -or $_.Extension -eq ".jpeg" -or $_.Extension -eq ".webp" -or $_.Extension -eq ".bmp" -or $_.Extension -eq ".gif") -and
        $_.Name -notlike "*.html"
    }
    if ($remainingImages.Count -eq 0) { $cleanGames++ }
}

Write-Host "Games with all images in assets/img: $cleanGames/$totalProcessed" -ForegroundColor Green
$cleanRate = [math]::Round(($cleanGames / $totalProcessed) * 100, 1)
Write-Host "Clean organization rate: $cleanRate%" -ForegroundColor Magenta

if ($cleanRate -eq 100) {
    Write-Host "`n🎉 Perfect! All images are now in assets/img folders!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  Some games may still have images outside assets/img" -ForegroundColor Yellow
}

Write-Host "`n📋 All images are now organized in assets/img folders!" -ForegroundColor Blue
